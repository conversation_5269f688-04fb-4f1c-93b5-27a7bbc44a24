using System;
using System.Collections.Generic;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Node types in the Statescript system
    /// </summary>
    public enum StatescriptNodeType
    {
        Entry,
        Action,
        Condition,
        Event,
        Variable,
        FlowControl,
        Custom
    }

    /// <summary>
    /// Execution state of a node
    /// </summary>
    public enum StatescriptNodeState
    {
        Inactive,
        Running,
        Success,
        Failure,
        Waiting
    }

    /// <summary>
    /// Base class for all Statescript nodes
    /// </summary>
    [MemoryPackable]
    [MemoryPackUnion(0, typeof(EntryNode))]
    [MemoryPackUnion(1, typeof(SequenceNode))]
    [MemoryPackUnion(2, typeof(BranchNode))]
    [MemoryPackUnion(3, typeof(WaitNode))]
    [MemoryPackUnion(4, typeof(LogNode))]
    [MemoryPackUnion(5, typeof(SetVariableNode))]
    [MemoryPackUnion(6, typeof(GetVariableNode))]
    [MemoryPackUnion(7, typeof(ParallelNode))]
    [MemoryPackUnion(8, typeof(EventTriggerNode))]
    [MemoryPackUnion(9, typeof(ConditionNode))]
    [MemoryPackUnion(10, typeof(LoopNode))]
    [MemoryPackUnion(11, typeof(FireEventNode))]
    [MemoryPackUnion(12, typeof(MultiEventTriggerNode))]
    public abstract partial class StatescriptNode
    {
        [MemoryPackInclude]
        public int Id { get; set; }

        [MemoryPackInclude]
        public string Name { get; set; } = string.Empty;

        [MemoryPackInclude]
        public string Description { get; set; } = string.Empty;

        [MemoryPackInclude]
        public StatescriptNodeType NodeType { get; protected set; }

        [MemoryPackInclude]
        public Dictionary<string, IVariable> Properties { get; set; } = new();

        /// <summary>
        /// Runtime-only data, not serialized
        /// </summary>
        [MemoryPackIgnore]
        public StatescriptNodeState State { get; protected set; } = StatescriptNodeState.Inactive;

        [MemoryPackIgnore]
        public bool IsActive => State == StatescriptNodeState.Running || State == StatescriptNodeState.Waiting;

        [MemoryPackIgnore]
        public StatescriptGraph Graph { get; private set; }

        [MemoryPackIgnore]
        public StatescriptContext Context { get; private set; }

        [MemoryPackConstructor]
        protected StatescriptNode()
        {
        }

        protected StatescriptNode(StatescriptNodeType nodeType)
        {
            NodeType = nodeType;
        }

        /// <summary>
        /// Initialize the node for runtime execution
        /// </summary>
        public virtual void Initialize(StatescriptGraph graph, StatescriptContext context)
        {
            Graph = graph;
            Context = context;
            State = StatescriptNodeState.Inactive;
        }

        /// <summary>
        /// Execute the node
        /// </summary>
        public void Execute()
        {
            if (State == StatescriptNodeState.Running || State == StatescriptNodeState.Waiting)
                return;

            State = StatescriptNodeState.Running;

            try
            {
                OnExecute();
            }
            catch (Exception ex)
            {
                Context?.LogError($"Error executing node '{Name}' (ID: {Id}): {ex.Message}");
                State = StatescriptNodeState.Failure;
                OnExecutionComplete(false);
            }
        }

        /// <summary>
        /// Update the node (called each frame while active)
        /// </summary>
        public virtual void Update(float deltaTime)
        {
            if (!IsActive) return;

            try
            {
                OnUpdate(deltaTime);
            }
            catch (Exception ex)
            {
                Context?.LogError($"Error updating node '{Name}' (ID: {Id}): {ex.Message}");
                State = StatescriptNodeState.Failure;
                OnExecutionComplete(false);
            }
        }

        /// <summary>
        /// Reset the node to inactive state
        /// </summary>
        public virtual void Reset()
        {
            State = StatescriptNodeState.Inactive;
            OnReset();
        }

        /// <summary>
        /// Validate the node configuration
        /// </summary>
        public virtual List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrEmpty(Name))
            {
                errors.Add($"Node (ID: {Id}) must have a name");
            }

            return errors;
        }

        /// <summary>
        /// Complete execution and trigger next nodes
        /// </summary>
        protected void CompleteExecution(bool success)
        {
            State = success ? StatescriptNodeState.Success : StatescriptNodeState.Failure;
            OnExecutionComplete(success);
        }

        /// <summary>
        /// Set the node to waiting state (for async operations)
        /// </summary>
        protected void SetWaiting()
        {
            State = StatescriptNodeState.Waiting;
        }

        /// <summary>
        /// Resume from waiting state
        /// </summary>
        protected void ResumeFromWaiting(bool success)
        {
            if (State != StatescriptNodeState.Waiting) return;
            CompleteExecution(success);
        }

        /// <summary>
        /// Trigger execution of connected nodes
        /// </summary>
        protected virtual void OnExecutionComplete(bool success)
        {
            var connections = Graph?.GetOutgoingConnections(Id);
            if (connections == null) return;

            foreach (var connection in connections)
            {
                // Check if connection condition is met
                if (ShouldTriggerConnection(connection, success))
                {
                    var nextNode = Graph.GetNode(connection.ToNodeId);
                    nextNode?.Execute();
                }
            }
        }

        /// <summary>
        /// Determine if a connection should be triggered based on execution result
        /// </summary>
        protected virtual bool ShouldTriggerConnection(StatescriptConnection connection, bool success)
        {
            // Default behavior: trigger all connections on success
            return success;
        }

        /// <summary>
        /// Get a property value with type safety
        /// </summary>
        public T GetProperty<T>(string key, T defaultValue = default)
        {
            if (Properties.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// Set a property value
        /// </summary>
        public void SetProperty<T>(string key, T value)
        {
            if (Properties.TryGetValue(key, out var existingValue) && existingValue is Variable<T> variable)
            {
                // Update existing variable value
                variable.Value = value;
                return;
            }

            // Create a new variable if it doesn't exist
            Properties[key] = new Variable<T>(value);
        }

        /// <summary>
        /// Override this to implement node-specific execution logic
        /// </summary>
        protected abstract void OnExecute();

        /// <summary>
        /// Override this to implement node-specific update logic
        /// </summary>
        protected virtual void OnUpdate(float deltaTime)
        {
            // Default implementation does nothing
        }

        /// <summary>
        /// Override this to implement node-specific reset logic
        /// </summary>
        protected virtual void OnReset()
        {
            // Default implementation does nothing
        }

        public override string ToString()
        {
            return $"{Name} ({NodeType})";
        }
    }

    /// <summary>
    /// Entry point node for the graph
    /// </summary>
    [MemoryPackable]
    public sealed partial class EntryNode : StatescriptNode
    {
        public EntryNode() : base(StatescriptNodeType.Entry)
        {
            Name = "Entry";
        }

        protected override void OnExecute()
        {
            // Entry node immediately completes and triggers next nodes
            CompleteExecution(true);
        }
    }

    /// <summary>
    /// Executes connected nodes in sequence
    /// </summary>
    [MemoryPackable]
    public sealed partial class SequenceNode : StatescriptNode
    {
        [MemoryPackInclude]
        public int CurrentIndex { get; set; }

        public SequenceNode() : base(StatescriptNodeType.FlowControl)
        {
            Name = "Sequence";
        }

        protected override void OnExecute()
        {
            CurrentIndex = 0;
            ExecuteNext();
        }

        protected override void OnReset()
        {
            CurrentIndex = 0;
        }

        private void ExecuteNext()
        {
            var connections = Graph?.GetOutgoingConnections(Id);
            if (connections == null || CurrentIndex >= connections.Count)
            {
                CompleteExecution(true);
                return;
            }

            var connection = connections[CurrentIndex];
            var nextNode = Graph.GetNode(connection.ToNodeId);
            nextNode?.Execute();
        }

        protected override void OnExecutionComplete(bool success)
        {
            if (!success)
            {
                base.OnExecutionComplete(false);
                return;
            }

            CurrentIndex++;
            ExecuteNext();
        }
    }

    /// <summary>
    /// Branches execution based on a condition
    /// </summary>
    [MemoryPackable]
    public sealed partial class BranchNode : StatescriptNode
    {
        public BranchNode() : base(StatescriptNodeType.FlowControl)
        {
            Name = "Branch";
        }

        protected override void OnExecute()
        {
            bool condition = EvaluateCondition();
            CompleteExecution(condition);
        }

        protected override bool ShouldTriggerConnection(StatescriptConnection connection, bool success)
        {
            // Branch node uses connection properties to determine which path to take
            var connectionType = connection.GetProperty<string>("Type", "Success");
            return (success && connectionType == "Success") || (!success && connectionType == "Failure");
        }

        private bool EvaluateCondition()
        {
            // This would be implemented based on the specific condition logic
            // For now, return a simple property-based condition
            return GetProperty<bool>("Condition", false);
        }
    }

    /// <summary>
    /// Waits for a specified duration
    /// </summary>
    [MemoryPackable]
    public sealed partial class WaitNode : StatescriptNode
    {
        [MemoryPackIgnore]
        private float _waitTime;

        [MemoryPackIgnore]
        private float _elapsedTime;

        public WaitNode() : base(StatescriptNodeType.Action)
        {
            Name = "Wait";
        }

        protected override void OnExecute()
        {
            _waitTime = GetProperty<float>("Duration", 1.0f);
            _elapsedTime = 0f;

            if (_waitTime <= 0f)
            {
                CompleteExecution(true);
            }
            else
            {
                SetWaiting();
            }
        }

        protected override void OnUpdate(float deltaTime)
        {
            if (State != StatescriptNodeState.Waiting) return;

            _elapsedTime += deltaTime;
            if (_elapsedTime >= _waitTime)
            {
                ResumeFromWaiting(true);
            }
        }

        protected override void OnReset()
        {
            _elapsedTime = 0f;
        }
    }

    /// <summary>
    /// Logs a message for debugging
    /// </summary>
    [MemoryPackable]
    public sealed partial class LogNode : StatescriptNode
    {
        public LogNode() : base(StatescriptNodeType.Action)
        {
            Name = "Log";
        }

        protected override void OnExecute()
        {
            var message = GetProperty<string>("Message", "Log message");
            Context?.LogInfo($"[Statescript] {message}");
            CompleteExecution(true);
        }
    }
}
