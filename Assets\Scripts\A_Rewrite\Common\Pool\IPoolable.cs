namespace GP.Pool
{
    /// <summary>
    /// We don't need a IPoolable
    /// using a IPoolable will make the Pool less flexible: 
    /// see if we want to use a Pool for List<T> or Dictionary<TKey, TValue>, and they are not IPoolable
    /// </summary>
    // public interface IPoolable
    // {
    //     void OnUse();
    //     void OnReset();
    // }

    public interface IPoolFactory<T> where T : class
    {
        T Create(object key = null);
        void Destroy(T obj);
        
        void OnUse(T obj);
        void OnReset(T obj);
    }

    public interface IPool
    {
        void Preload(int count, object key = null);
        void Clear();
    }
}