#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace Archery
{
    /// <summary>
    /// Custom editor for UnitStatusConfig with enhanced track-based action editing
    /// </summary>
    [CustomEditor(typeof(UnitStatusConfig))]
    public class UnitStatusConfigEditor : Editor
    {
        private SerializedProperty _nameProperty;
        private SerializedProperty _durationProperty;
        private SerializedProperty _categoryProperty;
        private SerializedProperty _actionsProperty;

        private bool _showPreview = false;
        private float _previewTime = 0f;
        private bool _isPlaying = false;

        private void OnEnable()
        {
            _nameProperty = serializedObject.FindProperty("Name");
            _durationProperty = serializedObject.FindProperty("Duration");
            _categoryProperty = serializedObject.FindProperty("Category");
            _actionsProperty = serializedObject.FindProperty("Actions");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Unit Status Configuration", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Basic properties
            DrawBasicProperties();
            EditorGUILayout.Space();

            // Actions section with track editor
            DrawActionsSection();
            EditorGUILayout.Space();

            // Preview section
            DrawPreviewSection();

            serializedObject.ApplyModifiedProperties();
        }

        private void DrawBasicProperties()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Basic Properties", EditorStyles.boldLabel);

            EditorGUILayout.PropertyField(_nameProperty);
            EditorGUILayout.PropertyField(_durationProperty);
            EditorGUILayout.PropertyField(_categoryProperty);

            EditorGUILayout.EndVertical();
        }

        private void DrawActionsSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Action Timeline", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Draw the custom track editor
            EditorGUILayout.PropertyField(_actionsProperty, true);

            // Action details for selected action
            DrawSelectedActionDetails();

            EditorGUILayout.EndVertical();
        }

        private void DrawSelectedActionDetails()
        {
            // This would be enhanced to show detailed properties of the selected action
            // For now, we'll show basic info
            if (_actionsProperty.arraySize > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Action Details", EditorStyles.boldLabel);

                // Find the selected action index from the track drawer
                // This is a simplified version - in a full implementation,
                // we'd need to communicate with the PropertyDrawer
                var selectedIndex = GetSelectedActionIndex();
                if (selectedIndex >= 0 && selectedIndex < _actionsProperty.arraySize)
                {
                    var selectedAction = _actionsProperty.GetArrayElementAtIndex(selectedIndex);
                    DrawActionProperties(selectedAction, selectedIndex);
                }
                else
                {
                    EditorGUILayout.HelpBox("Select an action in the timeline to edit its properties.", MessageType.Info);
                }
            }
        }

        private int GetSelectedActionIndex()
        {
            return UnitStatusActionTrackDrawer.GetSelectedActionIndex(_actionsProperty.propertyPath);
        }

        private void DrawActionProperties(SerializedProperty actionProperty, int index)
        {
            var managedRef = actionProperty.managedReferenceValue as UnitStatusAction;
            if (managedRef == null) return;

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField($"Action {index + 1}: {managedRef.GetType().Name}", EditorStyles.boldLabel);

            // Draw properties based on action type
            if (managedRef is TimeBasedAction timeBasedAction)
            {
                DrawTimeBasedActionProperties(actionProperty, timeBasedAction);
            }

            switch (managedRef)
            {
                case PlayAnimationAction animAction:
                    DrawPlayAnimationActionProperties(actionProperty, animAction);
                    break;
                case PlayAudioAction audioAction:
                    DrawPlayAudioActionProperties(actionProperty, audioAction);
                    break;
                case PlayEffectAction effectAction:
                    DrawPlayEffectActionProperties(actionProperty, effectAction);
                    break;
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawTimeBasedActionProperties(SerializedProperty actionProperty, TimeBasedAction action)
        {
            EditorGUI.BeginChangeCheck();

            var duration = EditorGUILayout.FloatField("Duration", action.Duration);
            var delay = EditorGUILayout.FloatField("Delay", action.Delay);

            if (EditorGUI.EndChangeCheck())
            {
                action.Duration = Mathf.Max(0f, duration);
                action.Delay = Mathf.Max(0f, delay);
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPlayAnimationActionProperties(SerializedProperty actionProperty, PlayAnimationAction action)
        {
            EditorGUI.BeginChangeCheck();

            var animationName = EditorGUILayout.TextField("Animation Name", action.AnimationName);

            if (EditorGUI.EndChangeCheck())
            {
                action.AnimationName = animationName;
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPlayAudioActionProperties(SerializedProperty actionProperty, PlayAudioAction action)
        {
            EditorGUI.BeginChangeCheck();

            var audioClip = EditorGUILayout.ObjectField("Audio Clip", action.AudioClip, typeof(AudioClip), false) as AudioClip;

            if (EditorGUI.EndChangeCheck())
            {
                action.AudioClip = audioClip;
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPlayEffectActionProperties(SerializedProperty actionProperty, PlayEffectAction action)
        {
            EditorGUI.BeginChangeCheck();

            var effectPrefab = EditorGUILayout.ObjectField("Effect Prefab", action.EffectPrefab, typeof(GameObject), false) as GameObject;

            if (EditorGUI.EndChangeCheck())
            {
                action.EffectPrefab = effectPrefab;
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPreviewSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Preview", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            _showPreview = EditorGUILayout.Toggle("Show Preview", _showPreview);

            if (_showPreview && _actionsProperty.arraySize > 0)
            {
                if (GUILayout.Button(_isPlaying ? "Stop" : "Play", GUILayout.Width(60)))
                {
                    _isPlaying = !_isPlaying;
                    if (_isPlaying)
                    {
                        _previewTime = 0f;
                    }
                }
            }

            EditorGUILayout.EndHorizontal();

            if (_showPreview)
            {
                var maxDuration = CalculateMaxDuration();
                _previewTime = EditorGUILayout.Slider("Time", _previewTime, 0f, maxDuration);

                // Preview timeline visualization would go here
                DrawPreviewTimeline(maxDuration);
            }

            EditorGUILayout.EndVertical();

            // Handle preview playback
            if (_isPlaying && _showPreview)
            {
                _previewTime += Time.deltaTime;
                var maxDuration = CalculateMaxDuration();
                if (_previewTime >= maxDuration)
                {
                    _previewTime = 0f;
                }
                Repaint();
            }
        }

        private float CalculateMaxDuration()
        {
            float maxDuration = 1f;
            var config = target as UnitStatusConfig;

            if (config?.Actions != null)
            {
                foreach (var action in config.Actions)
                {
                    if (action is TimeBasedAction timeBasedAction)
                    {
                        var actionEnd = timeBasedAction.Delay + timeBasedAction.Duration;
                        maxDuration = Mathf.Max(maxDuration, actionEnd);
                    }
                }
            }

            return maxDuration;
        }

        private void DrawPreviewTimeline(float maxDuration)
        {
            var rect = GUILayoutUtility.GetRect(0, 30f, GUILayout.ExpandWidth(true));

            // Background
            EditorGUI.DrawRect(rect, new Color(0.1f, 0.1f, 0.1f, 0.5f));

            // Current time indicator
            var timeX = rect.x + (_previewTime / maxDuration) * rect.width;
            var timeLineRect = new Rect(timeX, rect.y, 2f, rect.height);
            EditorGUI.DrawRect(timeLineRect, Color.red);

            // Time label
            var labelRect = new Rect(timeX + 5f, rect.y, 100f, rect.height);
            GUI.Label(labelRect, $"{_previewTime:F2}s", EditorStyles.miniLabel);
        }
    }
}
#endif
