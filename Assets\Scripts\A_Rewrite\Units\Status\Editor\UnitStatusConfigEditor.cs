#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace Archery
{
    /// <summary>
    /// Custom editor for UnitStatusConfig with enhanced track-based action editing
    /// </summary>
    [CustomEditor(typeof(UnitStatusConfig))]
    public class UnitStatusConfigEditor : Editor
    {
        private SerializedProperty _nameProperty;
        private SerializedProperty _durationProperty;
        private SerializedProperty _categoryProperty;
        private SerializedProperty _actionsProperty;

        private bool _showPreview = false;
        private float _previewTime = 0f;
        private bool _isPlaying = false;
        private int _selectedActionIndex = -1;

        // Track drawing constants
        private const float TRACK_HEIGHT = 30f;
        private const float TRACK_SPACING = 5f;
        private const float TIMELINE_HEIGHT = 20f;
        private const float BUTTON_HEIGHT = 20f;
        private const float TIME_SCALE = 100f; // pixels per second
        private const float MIN_TRACK_WIDTH = 50f;

        private static readonly Color[] TRACK_COLORS = new Color[]
        {
            new Color(0.8f, 0.4f, 0.4f, 0.8f), // Red
            new Color(0.4f, 0.8f, 0.4f, 0.8f), // Green
            new Color(0.4f, 0.4f, 0.8f, 0.8f), // Blue
            new Color(0.8f, 0.8f, 0.4f, 0.8f), // Yellow
            new Color(0.8f, 0.4f, 0.8f, 0.8f), // Magenta
            new Color(0.4f, 0.8f, 0.8f, 0.8f), // Cyan
        };

        private void OnEnable()
        {
            _nameProperty = serializedObject.FindProperty("Name");
            _durationProperty = serializedObject.FindProperty("Duration");
            _categoryProperty = serializedObject.FindProperty("Category");
            _actionsProperty = serializedObject.FindProperty("Actions");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Unit Status Configuration", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Basic properties
            DrawBasicProperties();
            EditorGUILayout.Space();

            // Actions section with track editor
            DrawActionsSection();
            EditorGUILayout.Space();

            // Preview section
            DrawPreviewSection();

            serializedObject.ApplyModifiedProperties();
        }

        private void DrawBasicProperties()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Basic Properties", EditorStyles.boldLabel);

            EditorGUILayout.PropertyField(_nameProperty);
            EditorGUILayout.PropertyField(_durationProperty);
            EditorGUILayout.PropertyField(_categoryProperty);

            EditorGUILayout.EndVertical();
        }

        private void DrawActionsSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Action Timeline", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Draw the custom track editor directly
            DrawActionTimeline();

            // Action details for selected action
            DrawSelectedActionDetails();

            EditorGUILayout.EndVertical();
        }

        private void DrawActionTimeline()
        {
            if (_actionsProperty.arraySize == 0)
            {
                EditorGUILayout.HelpBox("No actions added. Use the 'Add Action' button below to get started.", MessageType.Info);
                DrawActionButtons();
                return;
            }

            // Calculate timeline duration
            var maxDuration = CalculateMaxDuration();
            var timelineWidth = Mathf.Max(300f, maxDuration * TIME_SCALE);

            // Draw timeline header
            var timelineRect = GUILayoutUtility.GetRect(timelineWidth + 100f, TIMELINE_HEIGHT);
            DrawTimeline(new Rect(timelineRect.x + 80f, timelineRect.y, timelineWidth, timelineRect.height), maxDuration);

            // Draw action tracks
            for (int i = 0; i < _actionsProperty.arraySize; i++)
            {
                var actionProperty = _actionsProperty.GetArrayElementAtIndex(i);
                var trackRect = GUILayoutUtility.GetRect(timelineWidth + 100f, TRACK_HEIGHT);
                DrawActionTrack(trackRect, actionProperty, i, maxDuration, timelineWidth);
            }

            EditorGUILayout.Space();
            DrawActionButtons();
        }

        private void DrawTimeline(Rect rect, float maxDuration)
        {
            // Background
            EditorGUI.DrawRect(rect, new Color(0.2f, 0.2f, 0.2f, 0.5f));

            // Time markers
            var timeStep = GetTimeStep(maxDuration);
            for (float time = 0; time <= maxDuration; time += timeStep)
            {
                var x = rect.x + (time / maxDuration) * rect.width;
                var markerRect = new Rect(x, rect.y, 1f, rect.height);
                EditorGUI.DrawRect(markerRect, Color.white);

                // Time label
                var labelRect = new Rect(x + 2f, rect.y, 50f, rect.height);
                GUI.Label(labelRect, $"{time:F1}s", EditorStyles.miniLabel);
            }

            // Draw config duration boundary line
            var configDuration = _durationProperty.floatValue;
            if (configDuration > 0 && configDuration <= maxDuration)
            {
                var configX = rect.x + (configDuration / maxDuration) * rect.width;
                var configLineRect = new Rect(configX, rect.y, 3f, rect.height);
                EditorGUI.DrawRect(configLineRect, Color.yellow);

                // Config duration label
                var configLabelRect = new Rect(configX + 5f, rect.y, 100f, rect.height);
                var configLabelStyle = new GUIStyle(EditorStyles.miniLabel) { normal = { textColor = Color.yellow } };
                GUI.Label(configLabelRect, $"Config: {configDuration:F1}s", configLabelStyle);
            }
        }

        private float GetTimeStep(float maxDuration)
        {
            if (maxDuration <= 5f) return 0.5f;
            if (maxDuration <= 10f) return 1f;
            if (maxDuration <= 30f) return 2f;
            return 5f;
        }

        private void DrawActionTrack(Rect rect, SerializedProperty actionProperty, int index, float maxDuration, float timelineWidth)
        {
            var managedRef = actionProperty.managedReferenceValue as UnitStatusAction;
            if (managedRef == null) return;

            var isSelected = _selectedActionIndex == index;
            var trackColor = TRACK_COLORS[index % TRACK_COLORS.Length];

            if (isSelected)
                trackColor = Color.Lerp(trackColor, Color.white, 0.3f);

            // Action type label
            var labelRect = new Rect(rect.x, rect.y, 75f, rect.height);
            var actionTypeName = managedRef.GetType().Name.Replace("Action", "");
            GUI.Label(labelRect, actionTypeName, EditorStyles.miniLabel);

            // Track area
            var trackAreaRect = new Rect(rect.x + 80f, rect.y, timelineWidth, rect.height);

            if (managedRef is TimeBasedAction timeBasedAction)
            {
                DrawTimeBasedActionTrack(trackAreaRect, timeBasedAction, trackColor, maxDuration, index);
            }
            else
            {
                DrawInstantActionTrack(trackAreaRect, trackColor, index);
            }

            // Handle selection
            if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
            {
                _selectedActionIndex = index;
                Event.current.Use();
                GUI.changed = true;
            }
        }

        private void DrawTimeBasedActionTrack(Rect trackArea, TimeBasedAction action, Color color, float maxDuration, int index)
        {
            var startX = trackArea.x + (action.Delay / maxDuration) * trackArea.width;
            var width = Mathf.Max(MIN_TRACK_WIDTH, (action.Duration / maxDuration) * trackArea.width);

            var actionRect = new Rect(startX, trackArea.y + 2f, width, trackArea.height - 4f);

            // Check if action exceeds config duration
            var configDuration = _durationProperty.floatValue;
            var actionEndTime = action.Delay + action.Duration;
            var exceedsConfigDuration = actionEndTime > configDuration;

            // Modify color if exceeds config duration
            if (exceedsConfigDuration)
            {
                color = Color.Lerp(color, Color.red, 0.4f); // Blend with red
            }

            // Draw action bar
            EditorGUI.DrawRect(actionRect, color);

            // Draw border
            var borderColor = _selectedActionIndex == index ? Color.white : Color.black;
            if (exceedsConfigDuration)
            {
                borderColor = Color.red; // Red border for exceeding actions
            }
            DrawRectBorder(actionRect, borderColor, exceedsConfigDuration ? 2f : 1f);

            // Draw config duration line if action exceeds it
            if (exceedsConfigDuration && configDuration > 0)
            {
                var configLineX = trackArea.x + (configDuration / maxDuration) * trackArea.width;
                if (configLineX >= trackArea.x && configLineX <= trackArea.xMax)
                {
                    var configLineRect = new Rect(configLineX, trackArea.y, 2f, trackArea.height);
                    EditorGUI.DrawRect(configLineRect, Color.red);
                }
            }

            // Action details
            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter };
            var labelText = exceedsConfigDuration ? $"{action.Duration:F1}s!" : $"{action.Duration:F1}s";
            GUI.Label(actionRect, labelText, labelStyle);
        }

        private void DrawInstantActionTrack(Rect trackArea, Color color, int index)
        {
            var actionRect = new Rect(trackArea.x, trackArea.y + 2f, MIN_TRACK_WIDTH, trackArea.height - 4f);

            // Draw action bar
            EditorGUI.DrawRect(actionRect, color);

            // Draw border
            var borderColor = _selectedActionIndex == index ? Color.white : Color.black;
            DrawRectBorder(actionRect, borderColor, 1f);

            // Label
            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter };
            GUI.Label(actionRect, "Instant", labelStyle);
        }

        private void DrawRectBorder(Rect rect, Color color, float thickness)
        {
            // Top
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, rect.width, thickness), color);
            // Bottom
            EditorGUI.DrawRect(new Rect(rect.x, rect.yMax - thickness, rect.width, thickness), color);
            // Left
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, thickness, rect.height), color);
            // Right
            EditorGUI.DrawRect(new Rect(rect.xMax - thickness, rect.y, thickness, rect.height), color);
        }

        private void DrawActionButtons()
        {
            EditorGUILayout.BeginHorizontal();

            // Add Action dropdown
            if (GUILayout.Button("Add Action", GUILayout.Width(80)))
            {
                ShowAddActionMenu();
            }

            // Remove selected action
            if (_selectedActionIndex >= 0 && _selectedActionIndex < _actionsProperty.arraySize)
            {
                if (GUILayout.Button("Remove", GUILayout.Width(80)))
                {
                    _actionsProperty.DeleteArrayElementAtIndex(_selectedActionIndex);
                    _selectedActionIndex = -1;
                    serializedObject.ApplyModifiedProperties();
                }
            }

            // Clear all
            if (_actionsProperty.arraySize > 0)
            {
                if (GUILayout.Button("Clear All", GUILayout.Width(80)))
                {
                    if (EditorUtility.DisplayDialog("Clear All Actions",
                        "Are you sure you want to remove all actions?", "Yes", "Cancel"))
                    {
                        _actionsProperty.ClearArray();
                        _selectedActionIndex = -1;
                        serializedObject.ApplyModifiedProperties();
                    }
                }
            }

            // Sync Duration button
            if (_actionsProperty.arraySize > 0)
            {
                var maxDuration = CalculateMaxDuration();
                var currentDuration = _durationProperty.floatValue;

                if (Mathf.Abs(maxDuration - currentDuration) > 0.01f) // Only show if different
                {
                    var syncButtonContent = new GUIContent("Sync Duration",
                        $"Set Duration to {maxDuration:F2}s to match timeline");

                    if (GUILayout.Button(syncButtonContent, GUILayout.Width(100)))
                    {
                        _durationProperty.floatValue = maxDuration;
                        serializedObject.ApplyModifiedProperties();
                    }
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        private void ShowAddActionMenu()
        {
            var menu = new GenericMenu();

            menu.AddItem(new GUIContent("Play Animation Action"), false, () => AddAction<PlayAnimationAction>());
            menu.AddItem(new GUIContent("Play Audio Action"), false, () => AddAction<PlayAudioAction>());
            menu.AddItem(new GUIContent("Play Effect Action"), false, () => AddAction<PlayEffectAction>());

            menu.ShowAsContext();
        }

        private void AddAction<T>() where T : UnitStatusAction, new()
        {
            _actionsProperty.arraySize++;
            var newElement = _actionsProperty.GetArrayElementAtIndex(_actionsProperty.arraySize - 1);
            var newAction = new T();

            // Set default values for TimeBasedActions
            if (newAction is TimeBasedAction timeBasedAction)
            {
                timeBasedAction.Duration = 1.0f;
                timeBasedAction.Delay = 0.0f;
            }

            // Set default values for specific action types
            if (newAction is PlayAnimationAction animAction)
            {
                animAction.AnimationName = "DefaultAnimation";
            }

            newElement.managedReferenceValue = newAction;
            serializedObject.ApplyModifiedProperties();

            // Select the newly created action
            _selectedActionIndex = _actionsProperty.arraySize - 1;
        }

        private void DrawSelectedActionDetails()
        {
            // This would be enhanced to show detailed properties of the selected action
            // For now, we'll show basic info
            if (_actionsProperty.arraySize > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Action Details", EditorStyles.boldLabel);

                // Find the selected action index from the track drawer
                // This is a simplified version - in a full implementation,
                // we'd need to communicate with the PropertyDrawer
                var selectedIndex = GetSelectedActionIndex();
                if (selectedIndex >= 0 && selectedIndex < _actionsProperty.arraySize)
                {
                    var selectedAction = _actionsProperty.GetArrayElementAtIndex(selectedIndex);
                    DrawActionProperties(selectedAction, selectedIndex);
                }
                else
                {
                    EditorGUILayout.HelpBox("Select an action in the timeline to edit its properties.", MessageType.Info);
                }
            }
        }

        private int GetSelectedActionIndex()
        {
            return _selectedActionIndex;
        }

        private void DrawActionProperties(SerializedProperty actionProperty, int index)
        {
            var managedRef = actionProperty.managedReferenceValue as UnitStatusAction;
            if (managedRef == null) return;

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField($"Action {index + 1}: {managedRef.GetType().Name}", EditorStyles.boldLabel);

            // Draw properties based on action type
            if (managedRef is TimeBasedAction timeBasedAction)
            {
                DrawTimeBasedActionProperties(actionProperty, timeBasedAction);
            }

            switch (managedRef)
            {
                case PlayAnimationAction animAction:
                    DrawPlayAnimationActionProperties(actionProperty, animAction);
                    break;
                case PlayAudioAction audioAction:
                    DrawPlayAudioActionProperties(actionProperty, audioAction);
                    break;
                case PlayEffectAction effectAction:
                    DrawPlayEffectActionProperties(actionProperty, effectAction);
                    break;
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawTimeBasedActionProperties(SerializedProperty actionProperty, TimeBasedAction action)
        {
            EditorGUI.BeginChangeCheck();

            var configDuration = _durationProperty.floatValue;
            var actionEndTime = action.Delay + action.Duration;
            var exceedsConfigDuration = actionEndTime > configDuration;

            // Duration field with red color if it exceeds config duration
            var originalColor = GUI.color;
            if (exceedsConfigDuration)
            {
                GUI.color = new Color(1f, 0.6f, 0.6f); // Light red
            }

            var duration = EditorGUILayout.FloatField("Duration", action.Duration);

            GUI.color = originalColor; // Reset color

            var delay = EditorGUILayout.FloatField("Delay", action.Delay);

            // Show warning if action exceeds config duration
            if (exceedsConfigDuration)
            {
                EditorGUILayout.HelpBox(
                    $"Action end time ({actionEndTime:F2}s) exceeds Status Duration ({configDuration:F2}s). " +
                    "Consider reducing duration/delay or using 'Sync Duration' button.",
                    MessageType.Warning);
            }

            if (EditorGUI.EndChangeCheck())
            {
                action.Duration = Mathf.Max(0f, duration);
                action.Delay = Mathf.Max(0f, delay);
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPlayAnimationActionProperties(SerializedProperty actionProperty, PlayAnimationAction action)
        {
            EditorGUI.BeginChangeCheck();

            var animationName = EditorGUILayout.TextField("Animation Name", action.AnimationName);

            if (EditorGUI.EndChangeCheck())
            {
                action.AnimationName = animationName;
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPlayAudioActionProperties(SerializedProperty actionProperty, PlayAudioAction action)
        {
            EditorGUI.BeginChangeCheck();

            var audioClip = EditorGUILayout.ObjectField("Audio Clip", action.AudioClip, typeof(AudioClip), false) as AudioClip;

            if (EditorGUI.EndChangeCheck())
            {
                action.AudioClip = audioClip;
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPlayEffectActionProperties(SerializedProperty actionProperty, PlayEffectAction action)
        {
            EditorGUI.BeginChangeCheck();

            var effectPrefab = EditorGUILayout.ObjectField("Effect Prefab", action.EffectPrefab, typeof(GameObject), false) as GameObject;

            if (EditorGUI.EndChangeCheck())
            {
                action.EffectPrefab = effectPrefab;
                EditorUtility.SetDirty(target);
            }
        }

        private void DrawPreviewSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Preview", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            _showPreview = EditorGUILayout.Toggle("Show Preview", _showPreview);

            if (_showPreview && _actionsProperty.arraySize > 0)
            {
                if (GUILayout.Button(_isPlaying ? "Stop" : "Play", GUILayout.Width(60)))
                {
                    _isPlaying = !_isPlaying;
                    if (_isPlaying)
                    {
                        _previewTime = 0f;
                    }
                }
            }

            EditorGUILayout.EndHorizontal();

            if (_showPreview)
            {
                var maxDuration = CalculateMaxDuration();
                _previewTime = EditorGUILayout.Slider("Time", _previewTime, 0f, maxDuration);

                // Preview timeline visualization would go here
                DrawPreviewTimeline(maxDuration);
            }

            EditorGUILayout.EndVertical();

            // Handle preview playback
            if (_isPlaying && _showPreview)
            {
                _previewTime += Time.deltaTime;
                var maxDuration = CalculateMaxDuration();
                if (_previewTime >= maxDuration)
                {
                    _previewTime = 0f;
                }
                Repaint();
            }
        }

        private float CalculateMaxDuration()
        {
            float maxDuration = 1f;
            var config = target as UnitStatusConfig;

            if (config?.Actions != null)
            {
                foreach (var action in config.Actions)
                {
                    if (action is TimeBasedAction timeBasedAction)
                    {
                        var actionEnd = timeBasedAction.Delay + timeBasedAction.Duration;
                        maxDuration = Mathf.Max(maxDuration, actionEnd);
                    }
                }
            }

            return maxDuration;
        }

        private void DrawPreviewTimeline(float maxDuration)
        {
            var rect = GUILayoutUtility.GetRect(0, 30f, GUILayout.ExpandWidth(true));

            // Background
            EditorGUI.DrawRect(rect, new Color(0.1f, 0.1f, 0.1f, 0.5f));

            // Current time indicator
            var timeX = rect.x + (_previewTime / maxDuration) * rect.width;
            var timeLineRect = new Rect(timeX, rect.y, 2f, rect.height);
            EditorGUI.DrawRect(timeLineRect, Color.red);

            // Time label
            var labelRect = new Rect(timeX + 5f, rect.y, 100f, rect.height);
            GUI.Label(labelRect, $"{_previewTime:F2}s", EditorStyles.miniLabel);
        }
    }
}
#endif
