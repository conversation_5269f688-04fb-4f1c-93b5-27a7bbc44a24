using System.Collections.Generic;
using UnityEngine;
using GP.Pool;
using GP.Common;

namespace Archery
{
    public interface IUnitEvent : IEvent
    {
        int UnitId { get; } // 角色ID
    }

    public struct UnitHurtEvent : IUnitEvent
    {
        public int UnitId { get; private set; } // 角色ID
        public eDamageType DamageType { get; private set; } // 伤害类型
        public float Damage { get; private set; } // 伤害值

        public UnitHurtEvent(int unitId, eDamageType damageType, float damage)
        {
            UnitId = unitId;
            DamageType = damageType;
            Damage = damage;
        }
    }

    public struct UnitDieEvent : IUnitEvent
    {
        public int UnitId { get; private set; } // 角色ID

        public UnitDieEvent(int unitId)
        {
            UnitId = unitId;
        }
    }
}