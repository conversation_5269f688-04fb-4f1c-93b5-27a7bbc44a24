using System;
using System.Collections.Generic;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Triggers execution when a specific event occurs
    /// </summary>
    [MemoryPackable]
    public sealed partial class EventTriggerNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string EventName { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public bool AutoReset { get; set; } = true;

        [MemoryPackIgnore]
        private bool _hasTriggered;
        
        [MemoryPackIgnore]
        private Action<IEvent> _eventHandler;

        public EventTriggerNode() : base(StatescriptNodeType.Event)
        {
            Name = "Event Trigger";
        }

        public override void Initialize(StatescriptGraph graph, StatescriptContext context)
        {
            base.Initialize(graph, context);
            
            // Subscribe to the event
            // if (!string.IsNullOrEmpty(EventName) && context?.EventBus != null)
            // {
            //     _eventHandler = OnEventReceived;
            //     // This would need to be implemented based on your event system
            //     context.EventBus.Subscribe(EventName, _eventHandler);
            // }
        }

        protected override void OnExecute()
        {
            if (_hasTriggered && !AutoReset)
            {
                CompleteExecution(false);
                return;
            }

            // Event trigger nodes wait for events, they don't execute immediately
            SetWaiting();
        }

        protected override void OnReset()
        {
            _hasTriggered = false;
        }

        private void OnEventReceived(IEvent eventData)
        {
            if (State == StatescriptNodeState.Waiting)
            {
                _hasTriggered = true;
                
                // Store event data for other nodes to access
                SetProperty("EventData", eventData);
                
                ResumeFromWaiting(true);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();
            
            if (string.IsNullOrEmpty(EventName))
            {
                errors.Add($"Event Trigger node '{Name}' must specify an event name");
            }
            
            return errors;
        }
    }

    /// <summary>
    /// Fires an event into the event system
    /// </summary>
    [MemoryPackable]
    public sealed partial class FireEventNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string EventName { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public string EventDataVariable { get; set; } = string.Empty;

        public FireEventNode() : base(StatescriptNodeType.Event)
        {
            Name = "Fire Event";
        }

        protected override void OnExecute()
        {
            if (string.IsNullOrEmpty(EventName))
            {
                Context?.LogError($"Fire Event node '{Name}' has no event name specified");
                CompleteExecution(false);
                return;
            }

            try
            {
                // Get event data from variable if specified
                object eventData = null;
                if (!string.IsNullOrEmpty(EventDataVariable))
                {
                    eventData = Graph?.GetVariable(EventDataVariable)?.GetValueAsObject();
                }

                // Create a generic event with the data
                var gameEvent = new GenericStatescriptEvent(EventName, eventData);
                Context?.FireEvent(gameEvent);
                
                Context?.LogInfo($"Fired event '{EventName}' from node '{Name}'");
                CompleteExecution(true);
            }
            catch (Exception ex)
            {
                Context?.LogError($"Failed to fire event '{EventName}' from node '{Name}': {ex.Message}");
                CompleteExecution(false);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();
            
            if (string.IsNullOrEmpty(EventName))
            {
                errors.Add($"Fire Event node '{Name}' must specify an event name");
            }
            
            return errors;
        }
    }

    /// <summary>
    /// Generic event for Statescript system
    /// </summary>
    public class GenericStatescriptEvent : IEvent
    {
        public string EventName { get; }
        public object Data { get; }
        public DateTime Timestamp { get; }

        public GenericStatescriptEvent(string eventName, object data = null)
        {
            EventName = eventName;
            Data = data;
            Timestamp = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Waits for multiple events to occur
    /// </summary>
    [MemoryPackable]
    public sealed partial class MultiEventTriggerNode : StatescriptNode
    {
        [MemoryPackInclude]
        public System.Collections.Generic.List<string> EventNames { get; set; } = new();
        
        [MemoryPackInclude]
        public MultiEventMode Mode { get; set; } = MultiEventMode.All;
        
        [MemoryPackInclude]
        public float TimeoutSeconds { get; set; } = 0f; // 0 = no timeout

        [MemoryPackIgnore]
        private System.Collections.Generic.HashSet<string> _triggeredEvents = new();
        
        [MemoryPackIgnore]
        private float _elapsedTime;

        public MultiEventTriggerNode() : base(StatescriptNodeType.Event)
        {
            Name = "Multi Event Trigger";
        }

        protected override void OnExecute()
        {
            if (EventNames.Count == 0)
            {
                Context?.LogError($"Multi Event Trigger node '{Name}' has no events specified");
                CompleteExecution(false);
                return;
            }

            _triggeredEvents.Clear();
            _elapsedTime = 0f;
            
            // Subscribe to all events
            // This would need to be implemented based on your event system
            
            SetWaiting();
        }

        protected override void OnUpdate(float deltaTime)
        {
            if (State != StatescriptNodeState.Waiting) return;

            // Check timeout
            if (TimeoutSeconds > 0f)
            {
                _elapsedTime += deltaTime;
                if (_elapsedTime >= TimeoutSeconds)
                {
                    Context?.LogWarning($"Multi Event Trigger node '{Name}' timed out");
                    ResumeFromWaiting(false);
                    return;
                }
            }

            // Check if completion condition is met
            bool shouldComplete = Mode switch
            {
                MultiEventMode.All => _triggeredEvents.Count >= EventNames.Count,
                MultiEventMode.Any => _triggeredEvents.Count > 0,
                MultiEventMode.Majority => _triggeredEvents.Count > EventNames.Count / 2,
                _ => false
            };

            if (shouldComplete)
            {
                ResumeFromWaiting(true);
            }
        }

        protected override void OnReset()
        {
            _triggeredEvents.Clear();
            _elapsedTime = 0f;
        }

        private void OnEventReceived(string eventName)
        {
            if (EventNames.Contains(eventName))
            {
                _triggeredEvents.Add(eventName);
            }
        }

        public override System.Collections.Generic.List<string> Validate()
        {
            var errors = base.Validate();
            
            if (EventNames.Count == 0)
            {
                errors.Add($"Multi Event Trigger node '{Name}' must specify at least one event name");
            }
            
            return errors;
        }
    }

    /// <summary>
    /// Multi-event trigger modes
    /// </summary>
    public enum MultiEventMode
    {
        All,        // Wait for all events
        Any,        // Wait for any event
        Majority    // Wait for majority of events
    }
}
