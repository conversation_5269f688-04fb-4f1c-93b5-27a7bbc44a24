# UnitStatusConfig Track-Based Editor

## Overview
This implementation provides a track-based editing experience for the `List<UnitStatusAction> Actions` in `UnitStatusConfig`, similar to Unity's Timeline window.

## Features

### Track Visualization
- **Timeline Header**: Shows time markers (0.5s, 1s, 2s, etc.) based on the total duration
- **Action Tracks**: Each action is displayed as a colored horizontal bar
- **Time-Based Actions**: Show duration and delay visually on the timeline
- **Instant Actions**: Displayed as small bars at the beginning of the timeline
- **Color Coding**: Each action type has a different color for easy identification

### Interaction
- **Click to Select**: Click on any action track to select it
- **Add Actions**: Use the "Add Action" button to add new actions via dropdown menu
- **Remove Actions**: Select an action and click "Remove" to delete it
- **Clear All**: Remove all actions with confirmation dialog
- **Sync Duration**: Automatically sets UnitStatusConfig.Duration to match the timeline's maximum duration
- **Duration Validation**: Visual warnings when actions exceed the config duration

### Action Types Supported
1. **PlayAnimationAction**: Plays an animation with configurable duration and delay
2. **PlayAudioAction**: Plays an audio clip with configurable duration and delay
3. **PlayEffectAction**: Plays a visual effect with configurable duration and delay

### Property Editing
- **Selected Action Details**: When an action is selected in the track view, its properties are shown in the inspector
- **Real-time Updates**: Changes to action properties immediately update the track visualization
- **Default Values**: New actions are created with sensible default values

## Usage

### Creating a UnitStatusConfig
1. Use the menu: `Archery > Test > Create Sample Status Config` for a pre-configured example
2. Or use: `Archery > Test > Create Empty Status Config` to start from scratch
3. Or create manually: `Assets > Create > Archery > UnitStatusConfig`

### Editing Actions
1. Open a UnitStatusConfig in the inspector
2. Expand the "Actions" section to see the track editor
3. Use "Add Action" to add new actions
4. Click on action tracks to select and edit them
5. Use the property fields below the timeline to modify selected actions

### Timeline Features
- **Automatic Scaling**: Timeline automatically adjusts to fit all actions
- **Visual Feedback**: Selected actions are highlighted with a lighter color
- **Time Markers**: Grid lines show time intervals for easy alignment
- **Config Duration Line**: Yellow line shows the UnitStatusConfig.Duration boundary
- **Overflow Indication**: Actions exceeding config duration are highlighted in red
- **Smart Sync**: "Sync Duration" button appears when timeline extends beyond config duration

## Technical Implementation

### Files Created
- `UnitStatusActionTrackDrawer.cs`: Custom PropertyDrawer for track visualization
- `UnitStatusConfigEditor.cs`: Custom Editor with enhanced inspector
- `UnitStatusConfigTestMenu.cs`: Test menu items for creating sample assets
- `UnitStatusActionTrackAttribute`: Attribute to mark properties for track editing

### Key Classes
- `UnitStatusActionTrackAttribute`: PropertyAttribute to enable track editing
- `UnitStatusActionTrackDrawer`: PropertyDrawer that renders the timeline interface
- `UnitStatusConfigEditor`: Custom editor that integrates with the track drawer

### Integration Points
- Uses `[SerializeReference]` for polymorphic action storage
- Communicates between PropertyDrawer and Editor via static methods
- Maintains selection state across inspector redraws

## Future Enhancements
- Drag-and-drop reordering of actions
- Visual editing of duration/delay by dragging track edges
- Copy/paste actions between configs
- Undo/redo support for track operations
- Preview playback with timeline scrubbing
- Snap-to-grid functionality
- Multi-selection support
