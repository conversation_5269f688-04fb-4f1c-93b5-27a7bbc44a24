// #if UNITY_EDITOR
// using System.Collections.Generic;
// using System.Linq;
// using UnityEngine;
// using UnityEditor;
// using System.IO;

// namespace Archery
// {
//     /// <summary>
//     /// Custom PropertyDrawer for UnitStatusConfig lists with folder drag-and-drop support
//     /// </summary>
//     [CustomPropertyDrawer(typeof(UnitStatusConfigListAttribute))]
//     public class UnitStatusConfigListDrawer : PropertyDrawer
//     {
//         private const float HEADER_HEIGHT = 22f;
//         private const float ITEM_HEIGHT = 20f;
//         private const float BUTTON_HEIGHT = 18f;
//         private const float SPACING = 2f;
//         private const float INDENT = 15f;

//         private bool _isDragOver = false;
//         private static readonly Color DRAG_HIGHLIGHT_COLOR = new Color(0.3f, 0.6f, 1f, 0.3f);

//         public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
//         {
//             if (!property.isArray)
//                 return EditorGUIUtility.singleLineHeight;

//             var attr = attribute as UnitStatusConfigListAttribute;
//             var height = HEADER_HEIGHT + SPACING;

//             if (property.isExpanded)
//             {
//                 height += BUTTON_HEIGHT + SPACING; // Add/Clear buttons
//                 height += property.arraySize * (ITEM_HEIGHT + SPACING);
//                 height += SPACING; // Bottom padding
//             }

//             return height;
//         }

//         public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
//         {
//             if (!property.isArray)
//             {
//                 EditorGUI.LabelField(position, label.text, "UnitStatusConfigList can only be used on arrays/lists");
//                 return;
//             }

//             var attr = attribute as UnitStatusConfigListAttribute;
//             EditorGUI.BeginProperty(position, label, property);

//             var currentY = position.y;
//             var headerRect = new Rect(position.x, currentY, position.width, HEADER_HEIGHT);

//             // Handle drag and drop over the entire area
//             HandleDragAndDrop(position, property);

//             // Draw header with drag highlight
//             if (_isDragOver)
//             {
//                 EditorGUI.DrawRect(headerRect, DRAG_HIGHLIGHT_COLOR);
//             }

//             // Foldout header
//             property.isExpanded = EditorGUI.Foldout(headerRect, property.isExpanded,
//                 $"{attr.title} ({property.arraySize})", true);

//             currentY += HEADER_HEIGHT + SPACING;

//             if (property.isExpanded)
//             {
//                 EditorGUI.indentLevel++;

//                 // Draw buttons
//                 var buttonRect = new Rect(position.x + INDENT, currentY, position.width - INDENT, BUTTON_HEIGHT);
//                 DrawButtons(buttonRect, property);
//                 currentY += BUTTON_HEIGHT + SPACING;

//                 // Draw list items
//                 for (int i = 0; i < property.arraySize; i++)
//                 {
//                     var itemRect = new Rect(position.x + INDENT, currentY, position.width - INDENT, ITEM_HEIGHT);
//                     DrawListItem(itemRect, property, i);
//                     currentY += ITEM_HEIGHT + SPACING;
//                 }

//                 EditorGUI.indentLevel--;
//             }

//             EditorGUI.EndProperty();
//         }

//         private void HandleDragAndDrop(Rect dropArea, SerializedProperty property)
//         {
//             var currentEvent = Event.current;
//             var eventType = currentEvent.type;

//             if (eventType == EventType.DragUpdated || eventType == EventType.DragPerform)
//             {
//                 if (dropArea.Contains(currentEvent.mousePosition))
//                 {
//                     var draggedObjects = DragAndDrop.objectReferences;
//                     var hasValidObjects = false;

//                     // Check if any dragged objects are folders or UnitStatusConfig assets
//                     foreach (var obj in draggedObjects)
//                     {
//                         if (obj is DefaultAsset) // Folder
//                         {
//                             var path = AssetDatabase.GetAssetPath(obj);
//                             if (Directory.Exists(path))
//                             {
//                                 var configs = FindUnitStatusConfigsInFolder(path);
//                                 if (configs.Count > 0)
//                                 {
//                                     hasValidObjects = true;
//                                     break;
//                                 }
//                             }
//                         }
//                         else if (obj is UnitStatusConfig)
//                         {
//                             hasValidObjects = true;
//                             break;
//                         }
//                     }

//                     if (hasValidObjects)
//                     {
//                         DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
//                         _isDragOver = eventType == EventType.DragUpdated;

//                         if (eventType == EventType.DragPerform)
//                         {
//                             DragAndDrop.AcceptDrag();
//                             AddDraggedObjects(property, draggedObjects);
//                             _isDragOver = false;
//                         }

//                         currentEvent.Use();
//                     }
//                     else
//                     {
//                         _isDragOver = false;
//                     }
//                 }
//                 else
//                 {
//                     _isDragOver = false;
//                 }
//             }
//             else if (eventType == EventType.DragExited)
//             {
//                 _isDragOver = false;
//             }
//         }

//         private void AddDraggedObjects(SerializedProperty property, Object[] draggedObjects)
//         {
//             var configsToAdd = new List<UnitStatusConfig>();

//             foreach (var obj in draggedObjects)
//             {
//                 if (obj is DefaultAsset) // Folder
//                 {
//                     var path = AssetDatabase.GetAssetPath(obj);
//                     if (Directory.Exists(path))
//                     {
//                         var configs = FindUnitStatusConfigsInFolder(path);
//                         configsToAdd.AddRange(configs);
//                     }
//                 }
//                 else if (obj is UnitStatusConfig config)
//                 {
//                     configsToAdd.Add(config);
//                 }
//             }

//             // Remove duplicates and add to property
//             var existingConfigs = new HashSet<UnitStatusConfig>();
//             for (int i = 0; i < property.arraySize; i++)
//             {
//                 var element = property.GetArrayElementAtIndex(i);
//                 if (element.objectReferenceValue is UnitStatusConfig existing)
//                 {
//                     existingConfigs.Add(existing);
//                 }
//             }

//             foreach (var config in configsToAdd)
//             {
//                 if (!existingConfigs.Contains(config))
//                 {
//                     property.arraySize++;
//                     var newElement = property.GetArrayElementAtIndex(property.arraySize - 1);
//                     newElement.objectReferenceValue = config;
//                 }
//             }

//             property.serializedObject.ApplyModifiedProperties();
//         }

//         private List<UnitStatusConfig> FindUnitStatusConfigsInFolder(string folderPath)
//         {
//             var configs = new List<UnitStatusConfig>();
//             var guids = AssetDatabase.FindAssets("t:UnitStatusConfig", new[] { folderPath });

//             foreach (var guid in guids)
//             {
//                 var assetPath = AssetDatabase.GUIDToAssetPath(guid);
//                 var config = AssetDatabase.LoadAssetAtPath<UnitStatusConfig>(assetPath);
//                 if (config != null)
//                 {
//                     configs.Add(config);
//                 }
//             }

//             return configs;
//         }

//         private void DrawButtons(Rect rect, SerializedProperty property)
//         {
//             var buttonWidth = 60f;
//             var spacing = 5f;

//             // Add button
//             var addRect = new Rect(rect.x, rect.y, buttonWidth, rect.height);
//             if (GUI.Button(addRect, "Add", EditorStyles.miniButtonLeft))
//             {
//                 property.arraySize++;
//                 property.serializedObject.ApplyModifiedProperties();
//             }

//             // Clear button
//             var clearRect = new Rect(addRect.xMax + spacing, rect.y, buttonWidth, rect.height);
//             if (GUI.Button(clearRect, "Clear", EditorStyles.miniButtonRight))
//             {
//                 if (EditorUtility.DisplayDialog("Clear All",
//                     "Are you sure you want to remove all status configs?", "Yes", "Cancel"))
//                 {
//                     property.ClearArray();
//                     property.serializedObject.ApplyModifiedProperties();
//                 }
//             }

//             // Remove duplicates button
//             if (property.arraySize > 1)
//             {
//                 var removeDuplicatesRect = new Rect(clearRect.xMax + spacing, rect.y, 80f, rect.height);
//                 if (GUI.Button(removeDuplicatesRect, "Remove Dupes", EditorStyles.miniButton))
//                 {
//                     RemoveDuplicates(property);
//                 }
//                 clearRect = removeDuplicatesRect; // Update for info text positioning
//             }

//             // Info text
//             var infoRect = new Rect(clearRect.xMax + spacing * 2, rect.y, rect.width - clearRect.xMax - spacing * 2, rect.height);
//             var infoStyle = new GUIStyle(EditorStyles.miniLabel)
//             {
//                 alignment = TextAnchor.MiddleLeft,
//                 normal = { textColor = _isDragOver ? Color.cyan : Color.gray }
//             };
//             var infoText = _isDragOver ? "Drop to add configs!" : "Drag folders or UnitStatusConfig assets here";
//             GUI.Label(infoRect, infoText, infoStyle);
//         }

//         private void RemoveDuplicates(SerializedProperty property)
//         {
//             var uniqueConfigs = new HashSet<UnitStatusConfig>();
//             var indicesToRemove = new List<int>();

//             // Find duplicates
//             for (int i = 0; i < property.arraySize; i++)
//             {
//                 var element = property.GetArrayElementAtIndex(i);
//                 var config = element.objectReferenceValue as UnitStatusConfig;

//                 if (config == null || uniqueConfigs.Contains(config))
//                 {
//                     indicesToRemove.Add(i);
//                 }
//                 else
//                 {
//                     uniqueConfigs.Add(config);
//                 }
//             }

//             // Remove duplicates (in reverse order to maintain indices)
//             for (int i = indicesToRemove.Count - 1; i >= 0; i--)
//             {
//                 property.DeleteArrayElementAtIndex(indicesToRemove[i]);
//             }

//             if (indicesToRemove.Count > 0)
//             {
//                 property.serializedObject.ApplyModifiedProperties();
//                 Debug.Log($"Removed {indicesToRemove.Count} duplicate status configs.");
//             }
//         }

//         private void DrawListItem(Rect rect, SerializedProperty property, int index)
//         {
//             var element = property.GetArrayElementAtIndex(index);
//             var buttonWidth = 20f;
//             var config = element.objectReferenceValue as UnitStatusConfig;

//             // Object field
//             var objectRect = new Rect(rect.x, rect.y, rect.width - buttonWidth - 5f, rect.height);

//             // Custom label for better display
//             if (config != null)
//             {
//                 var labelText = $"{config.name} ({config.Duration:F1}s, {config.Actions?.Count ?? 0} actions)";
//                 var labelContent = new GUIContent(labelText);

//                 EditorGUI.BeginChangeCheck();
//                 var newConfig = EditorGUI.ObjectField(objectRect, labelContent, config, typeof(UnitStatusConfig), false) as UnitStatusConfig;
//                 if (EditorGUI.EndChangeCheck())
//                 {
//                     element.objectReferenceValue = newConfig;
//                     property.serializedObject.ApplyModifiedProperties();
//                 }
//             }
//             else
//             {
//                 EditorGUI.PropertyField(objectRect, element, GUIContent.none);
//             }

//             // Remove button
//             var removeRect = new Rect(objectRect.xMax + 5f, rect.y, buttonWidth, rect.height);
//             if (GUI.Button(removeRect, "×", EditorStyles.miniButton))
//             {
//                 property.DeleteArrayElementAtIndex(index);
//                 property.serializedObject.ApplyModifiedProperties();
//             }
//         }
//     }
// }
// #endif
