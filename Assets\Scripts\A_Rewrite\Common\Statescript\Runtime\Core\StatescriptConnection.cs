using System;
using System.Collections.Generic;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Represents a connection between two nodes in a Statescript graph
    /// </summary>
    [MemoryPackable]
    public sealed partial class StatescriptConnection
    {
        [MemoryPackInclude]
        public int Id { get; set; }
        
        [MemoryPackInclude]
        public int FromNodeId { get; set; }
        
        [MemoryPackInclude]
        public int ToNodeId { get; set; }
        
        [MemoryPackInclude]
        public string FromPortName { get; set; } = "Out";
        
        [MemoryPackInclude]
        public string ToPortName { get; set; } = "In";
        
        [MemoryPackInclude]
        public Dictionary<string, object> Properties { get; set; } = new();

        [MemoryPackConstructor]
        public StatescriptConnection()
        {
        }

        public StatescriptConnection(int fromNodeId, int toNodeId)
        {
            FromNodeId = fromNodeId;
            ToNodeId = toNodeId;
        }

        public StatescriptConnection(int fromNodeId, int toNodeId, string fromPort, string toPort)
        {
            FromNodeId = fromNodeId;
            ToNodeId = toNodeId;
            FromPortName = fromPort;
            ToPortName = toPort;
        }

        /// <summary>
        /// Get a property value with type safety
        /// </summary>
        public T GetProperty<T>(string key, T defaultValue = default)
        {
            if (Properties.TryGetValue(key, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// Set a property value
        /// </summary>
        public void SetProperty<T>(string key, T value)
        {
            Properties[key] = value;
        }

        public override string ToString()
        {
            return $"Connection: {FromNodeId}:{FromPortName} -> {ToNodeId}:{ToPortName}";
        }
    }
}
