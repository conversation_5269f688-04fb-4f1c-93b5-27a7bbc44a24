using System;
using System.Collections.Generic;
using UnityEngine;
using GP.Pool;
using GP.Common;
using UnityEngine.AddressableAssets;

namespace Archery
{
    /// <summary>
    /// Attribute to mark a List<UnitStatusAction> for track-based editing
    /// </summary>
    public class UnitStatusActionTrackAttribute : PropertyAttribute
    {
    }

    public enum eActionStatus : byte
    {
        None, Running, Finished
    }

    [Serializable]
    public abstract class UnitStatusAction
    {
        public abstract void Execute(Unit target);
        public abstract void Tick(Unit target, float deltaTime);
        public abstract void Abort(Unit target);
        public abstract void Reset();
    }

    [Serializable]
    public abstract class TimeBasedAction : UnitStatusAction
    {
        public float Duration;
        public float Delay;

        [NonSerialized]
        public float ElapsedTime;

        public eActionStatus Status { get; protected set; } = eActionStatus.None;

        public override void Execute(Unit target)
        {
            Reset();
            Tick(target, 0f); // Initialize the action
        }

        public override void Reset()
        {
            ElapsedTime = 0f;
            Status = eActionStatus.None; // Reset status
        }

        public override void Tick(Unit target, float deltaTime)
        {
            ElapsedTime += deltaTime;
            if (ElapsedTime < Delay)
            {
                return; // Wait for delay to complete
            }
            if (Status == eActionStatus.None)
            {
                OnStart(target); // Start the action if not already running
            }
            if (ElapsedTime >= Duration)
            {
                OnComplete(target);
            }
            else
            {
                OnUpdate(target);
            }
        }

        public override void Abort(Unit target)
        {
            Status = eActionStatus.Finished;
        }

        protected virtual void OnStart(Unit target)
        {
            Status = eActionStatus.Running;
        }
        protected virtual void OnUpdate(Unit target)
        {
        }
        protected virtual void OnComplete(Unit target)
        {
            Status = eActionStatus.Finished;
        }
    }

    [Serializable]
    public class PlayAnimationAction : TimeBasedAction
    {
        public string AnimationName;

        protected override void OnStart(Unit target)
        {
            base.OnStart(target);

        }

        protected override void OnComplete(Unit target)
        {
            base.OnComplete(target);
            // Optionally reset the animation state or perform cleanup
        }
    }

    [Serializable]
    public class PlayAudioAction : TimeBasedAction
    {
        public AudioClip AudioClip;

        protected override void OnStart(Unit target)
        {
            base.OnStart(target);
            // Play the audio clip on the target unit

        }

        protected override void OnComplete(Unit target)
        {
            base.OnComplete(target);
            // Optionally reset the audio state or perform cleanup
        }
    }

    [Serializable]
    public class PlayEffectAction : TimeBasedAction
    {
        public GameObject EffectPrefab;

        protected override void OnStart(Unit target)
        {
            base.OnStart(target);
            // Instantiate the effect prefab on the target unit
        }

        protected override void OnComplete(Unit target)
        {
            base.OnComplete(target);
            // Optionally destroy the effect instance or perform cleanup
        }
    }
}