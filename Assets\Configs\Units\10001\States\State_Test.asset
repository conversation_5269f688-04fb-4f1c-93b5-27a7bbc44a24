%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: State_Test
  m_EditorClassIdentifier: Assembly-CSharp:Archery:UnitStatusConfig
  Name: OnHurt
  Duration: 1.6800001
  Category: 0
  Actions:
  - rid: -2
  - rid: 6753504565633220683
  - rid: 6753504565633220684
  - rid: 6753504565633220685
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 6753504565633220683
      type: {class: PlayAnimationAction, ns: Archery, asm: Assembly-CSharp}
      data:
        Duration: 0
        Delay: 0
        AnimationName: DefaultAnimation
    - rid: 6753504565633220684
      type: {class: PlayAudioAction, ns: Archery, asm: Assembly-CSharp}
      data:
        Duration: 1.35
        Delay: 0.33
        AudioClip: {fileID: 0}
    - rid: 6753504565633220685
      type: {class: PlayEffectAction, ns: Archery, asm: Assembly-CSharp}
      data:
        Duration: 1
        Delay: 0
        EffectPrefab: {fileID: 0}
