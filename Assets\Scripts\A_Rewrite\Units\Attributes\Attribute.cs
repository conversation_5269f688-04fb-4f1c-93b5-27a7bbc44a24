using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace Archery
{
    public interface IAttribute
    {
        bool IsDirty { get; }
        void ApplyModifiers(bool notify = true);
        void NotifyValueChanged();
    }
    
    public class Attribute<T> : IAttribute
    {
        private T _baseValue;
        private T _currentValue;
        private bool _isDirty = true;

        private readonly List<Modifier<T>> _modifiers = new List<Modifier<T>>();
        
        public event Action<T> OnValueChanged;

        public T BaseValue 
        { 
            get { return _baseValue; }
            set
            {
                if (!EqualityComparer<T>.Default.Equals(_baseValue, value)) 
                {
                    _baseValue = value;
                    _isDirty = true;
                }
            }
        }
        
        public T CurrentValue 
        { 
            get 
            {
                return _currentValue; 
            }
            set 
            {
                if (!EqualityComparer<T>.Default.Equals(_currentValue, value))
                {
                    _currentValue = value;
                    _isDirty = true;
                }
            }
        }

        public bool IsDirty => _isDirty;

        public Attribute(T baseValue)
        {
            _baseValue = baseValue;
            _currentValue = baseValue;
        }

        public void AddModifier(Modifier<T> modifier)
        {
            _modifiers.Add(modifier);
            _isDirty = true;
        }

        public void RemoveModifier(Modifier<T> modifier)
        {
            _modifiers.Remove(modifier);
            _isDirty = true;
        }

        public void ApplyModifiers(bool notify = true)
        {
            UpdateCurrentValue();
            if (notify)
            {
                NotifyValueChanged();
            }
        }

        public void NotifyValueChanged()
        {
            if (_isDirty)
            {
                OnValueChanged?.Invoke(_currentValue);
            }
        }

        private void UpdateCurrentValue()
        {
            T newValue = _baseValue;

            foreach (var modifier in _modifiers)
            {
                if (modifier.ModifierType == eModifierType.Flat)
                {
                    if (modifier.Operation == eModiferOperation.Add)
                    {
                        newValue = Add(newValue, modifier.Value);
                    }
                    else if (modifier.Operation == eModiferOperation.Multiply)
                    {
                        newValue = Multiply(newValue, modifier.Value);
                    }
                }
                else if (modifier.ModifierType == eModifierType.Percent)
                {
                    if (modifier.Operation == eModiferOperation.Add)
                    {
                        newValue = Add(newValue, Multiply(_baseValue, modifier.Value));
                    }
                    else if (modifier.Operation == eModiferOperation.Multiply)
                    {
                        newValue = Multiply(newValue, Multiply(_baseValue, modifier.Value));
                    }
                }
            }
            
            CurrentValue = newValue;
        }
        
        private T Add(T a, T b) => Arithmetic<T>.Add(a, b);

        private T Multiply(T a, T b) => Arithmetic<T>.Multiply(a, b);
    }

    public static class Arithmetic<T>
    {
        public static readonly Func<T, T, T> Add;
        public static readonly Func<T, T, T> Multiply;

        static Arithmetic()
        {
            Add = CreateBinary(Expression.Add);
            Multiply = CreateBinary(Expression.Multiply);
        }

        private static Func<T, T, T> CreateBinary(Func<Expression, Expression, BinaryExpression> op)
        {
            var a = Expression.Parameter(typeof(T), "a");
            var b = Expression.Parameter(typeof(T), "b");
            try
            {
                var body = op(a, b);
                return Expression.Lambda<Func<T, T, T>>(body, a, b).Compile();
            }
            catch
            {
                // Not supported for this T
                return (x, y) => throw new NotSupportedException($"Operation not supported for type {typeof(T)}");
            }
        }
    }
}