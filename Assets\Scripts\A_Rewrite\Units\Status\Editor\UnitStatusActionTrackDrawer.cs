#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;

namespace Archery
{
    /// <summary>
    /// Custom PropertyDrawer for track-based editing of UnitStatusAction lists
    /// </summary>
    [CustomPropertyDrawer(typeof(UnitStatusActionTrackAttribute))]
    public class UnitStatusActionTrackDrawer : PropertyDrawer
    {
        private const float TRACK_HEIGHT = 30f;
        private const float TRACK_SPACING = 5f;
        private const float TIMELINE_HEIGHT = 20f;
        private const float HEADER_HEIGHT = 25f;
        private const float BUTTON_HEIGHT = 20f;
        private const float TIME_SCALE = 100f; // pixels per second
        private const float MIN_TRACK_WIDTH = 50f;

        private static readonly Color[] TRACK_COLORS = new Color[]
        {
            new Color(0.8f, 0.4f, 0.4f, 0.8f), // Red
            new Color(0.4f, 0.8f, 0.4f, 0.8f), // Green
            new Color(0.4f, 0.4f, 0.8f, 0.8f), // Blue
            new Color(0.8f, 0.8f, 0.4f, 0.8f), // Yellow
            new Color(0.8f, 0.4f, 0.8f, 0.8f), // Magenta
            new Color(0.4f, 0.8f, 0.8f, 0.8f), // Cyan
        };

        private static int _selectedActionIndex = -1;
        private static string _selectedPropertyPath = "";
        private bool _isDragging = false;
        private Vector2 _dragStartPos;
        private int _draggedActionIndex = -1;
        private bool _showActionDetails = false;

        /// <summary>
        /// Get the currently selected action index for the given property path
        /// </summary>
        public static int GetSelectedActionIndex(string propertyPath)
        {
            return _selectedPropertyPath == propertyPath ? _selectedActionIndex : -1;
        }

        /// <summary>
        /// Get the currently selected action for the given property
        /// </summary>
        public static UnitStatusAction GetSelectedAction(SerializedProperty property)
        {
            var selectedIndex = GetSelectedActionIndex(property.propertyPath);
            if (selectedIndex >= 0 && selectedIndex < property.arraySize)
            {
                var actionProperty = property.GetArrayElementAtIndex(selectedIndex);
                return actionProperty.managedReferenceValue as UnitStatusAction;
            }
            return null;
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            if (!property.isExpanded)
                return EditorGUIUtility.singleLineHeight;

            var arraySize = property.arraySize;
            var totalHeight = HEADER_HEIGHT + TIMELINE_HEIGHT + BUTTON_HEIGHT + 10f; // padding
            totalHeight += arraySize * (TRACK_HEIGHT + TRACK_SPACING);

            return totalHeight;
        }

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            var currentY = position.y;

            // Header with foldout
            var headerRect = new Rect(position.x, currentY, position.width, HEADER_HEIGHT);
            property.isExpanded = EditorGUI.Foldout(headerRect, property.isExpanded,
                $"{label.text} ({property.arraySize} actions)", true);

            if (!property.isExpanded)
            {
                EditorGUI.EndProperty();
                return;
            }

            currentY += HEADER_HEIGHT;

            // Calculate timeline duration
            var maxDuration = CalculateMaxDuration(property);
            var timelineWidth = Mathf.Max(position.width - 100f, maxDuration * TIME_SCALE);

            // Draw timeline
            var timelineRect = new Rect(position.x + 80f, currentY, timelineWidth, TIMELINE_HEIGHT);
            DrawTimeline(timelineRect, maxDuration);
            currentY += TIMELINE_HEIGHT + 5f;

            // Draw action tracks
            for (int i = 0; i < property.arraySize; i++)
            {
                var actionProperty = property.GetArrayElementAtIndex(i);
                var trackRect = new Rect(position.x, currentY, position.width, TRACK_HEIGHT);
                DrawActionTrack(trackRect, actionProperty, i, maxDuration, timelineWidth);
                currentY += TRACK_HEIGHT + TRACK_SPACING;
            }

            // Add/Remove buttons
            var buttonRect = new Rect(position.x, currentY, position.width, BUTTON_HEIGHT);
            DrawActionButtons(buttonRect, property);

            EditorGUI.EndProperty();
        }

        private float CalculateMaxDuration(SerializedProperty property)
        {
            float maxDuration = 5f; // minimum timeline duration

            for (int i = 0; i < property.arraySize; i++)
            {
                var actionProperty = property.GetArrayElementAtIndex(i);
                var managedRef = actionProperty.managedReferenceValue as UnitStatusAction;

                if (managedRef is TimeBasedAction timeBasedAction)
                {
                    var actionEnd = timeBasedAction.Delay + timeBasedAction.Duration;
                    maxDuration = Mathf.Max(maxDuration, actionEnd);
                }
            }

            return maxDuration + 1f; // Add some padding
        }

        private void DrawTimeline(Rect rect, float maxDuration)
        {
            // Background
            EditorGUI.DrawRect(rect, new Color(0.2f, 0.2f, 0.2f, 0.5f));

            // Time markers
            var timeStep = GetTimeStep(maxDuration);
            for (float time = 0; time <= maxDuration; time += timeStep)
            {
                var x = rect.x + (time / maxDuration) * rect.width;
                var markerRect = new Rect(x, rect.y, 1f, rect.height);
                EditorGUI.DrawRect(markerRect, Color.white);

                // Time label
                var labelRect = new Rect(x + 2f, rect.y, 50f, rect.height);
                GUI.Label(labelRect, $"{time:F1}s", EditorStyles.miniLabel);
            }
        }

        private float GetTimeStep(float maxDuration)
        {
            if (maxDuration <= 5f) return 0.5f;
            if (maxDuration <= 10f) return 1f;
            if (maxDuration <= 30f) return 2f;
            return 5f;
        }

        private void DrawActionTrack(Rect rect, SerializedProperty actionProperty, int index, float maxDuration, float timelineWidth)
        {
            var managedRef = actionProperty.managedReferenceValue as UnitStatusAction;
            if (managedRef == null) return;

            var isSelected = _selectedActionIndex == index && _selectedPropertyPath == actionProperty.propertyPath.Replace($".Array.data[{index}]", "");
            var trackColor = TRACK_COLORS[index % TRACK_COLORS.Length];

            if (isSelected)
                trackColor = Color.Lerp(trackColor, Color.white, 0.3f);

            // Action type label
            var labelRect = new Rect(rect.x, rect.y, 75f, rect.height);
            var actionTypeName = managedRef.GetType().Name.Replace("Action", "");
            GUI.Label(labelRect, actionTypeName, EditorStyles.miniLabel);

            // Track area
            var trackAreaRect = new Rect(rect.x + 80f, rect.y, timelineWidth, rect.height);

            if (managedRef is TimeBasedAction timeBasedAction)
            {
                DrawTimeBasedActionTrack(trackAreaRect, timeBasedAction, trackColor, maxDuration, index);
            }
            else
            {
                DrawInstantActionTrack(trackAreaRect, trackColor, index);
            }

            // Handle selection
            if (Event.current.type == EventType.MouseDown && rect.Contains(Event.current.mousePosition))
            {
                _selectedActionIndex = index;
                _selectedPropertyPath = actionProperty.propertyPath.Replace($".Array.data[{index}]", "");
                Event.current.Use();
                GUI.changed = true;
            }
        }

        private void DrawTimeBasedActionTrack(Rect trackArea, TimeBasedAction action, Color color, float maxDuration, int index)
        {
            var startX = trackArea.x + (action.Delay / maxDuration) * trackArea.width;
            var width = Mathf.Max(MIN_TRACK_WIDTH, (action.Duration / maxDuration) * trackArea.width);

            var actionRect = new Rect(startX, trackArea.y + 2f, width, trackArea.height - 4f);

            // Draw action bar
            EditorGUI.DrawRect(actionRect, color);

            // Draw border
            var borderColor = _selectedActionIndex == index ? Color.white : Color.black;
            DrawRectBorder(actionRect, borderColor, 1f);

            // Action details
            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter };
            GUI.Label(actionRect, $"{action.Duration:F1}s", labelStyle);
        }

        private void DrawInstantActionTrack(Rect trackArea, Color color, int index)
        {
            var actionRect = new Rect(trackArea.x, trackArea.y + 2f, MIN_TRACK_WIDTH, trackArea.height - 4f);

            // Draw action bar
            EditorGUI.DrawRect(actionRect, color);

            // Draw border
            var borderColor = _selectedActionIndex == index ? Color.white : Color.black;
            DrawRectBorder(actionRect, borderColor, 1f);

            // Label
            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter };
            GUI.Label(actionRect, "Instant", labelStyle);
        }

        private void DrawRectBorder(Rect rect, Color color, float thickness)
        {
            // Top
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, rect.width, thickness), color);
            // Bottom
            EditorGUI.DrawRect(new Rect(rect.x, rect.yMax - thickness, rect.width, thickness), color);
            // Left
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, thickness, rect.height), color);
            // Right
            EditorGUI.DrawRect(new Rect(rect.xMax - thickness, rect.y, thickness, rect.height), color);
        }

        private void DrawActionButtons(Rect rect, SerializedProperty property)
        {
            var buttonWidth = 80f;
            var spacing = 5f;
            var currentX = rect.x;

            // Add Action dropdown
            var addRect = new Rect(currentX, rect.y, buttonWidth, rect.height);
            if (GUI.Button(addRect, "Add Action"))
            {
                ShowAddActionMenu(property);
            }
            currentX += buttonWidth + spacing;

            // Remove selected action
            var selectedIndex = GetSelectedActionIndex(property.propertyPath);
            if (selectedIndex >= 0 && selectedIndex < property.arraySize)
            {
                var removeRect = new Rect(currentX, rect.y, buttonWidth, rect.height);
                if (GUI.Button(removeRect, "Remove"))
                {
                    property.DeleteArrayElementAtIndex(selectedIndex);
                    _selectedActionIndex = -1;
                    _selectedPropertyPath = "";
                    property.serializedObject.ApplyModifiedProperties();
                }
                currentX += buttonWidth + spacing;
            }

            // Clear all
            if (property.arraySize > 0)
            {
                var clearRect = new Rect(currentX, rect.y, buttonWidth, rect.height);
                if (GUI.Button(clearRect, "Clear All"))
                {
                    if (EditorUtility.DisplayDialog("Clear All Actions",
                        "Are you sure you want to remove all actions?", "Yes", "Cancel"))
                    {
                        property.ClearArray();
                        _selectedActionIndex = -1;
                        _selectedPropertyPath = "";
                        property.serializedObject.ApplyModifiedProperties();
                    }
                }
            }
        }

        private void ShowAddActionMenu(SerializedProperty property)
        {
            var menu = new GenericMenu();

            menu.AddItem(new GUIContent("Play Animation Action"), false, () => AddAction<PlayAnimationAction>(property));
            menu.AddItem(new GUIContent("Play Audio Action"), false, () => AddAction<PlayAudioAction>(property));
            menu.AddItem(new GUIContent("Play Effect Action"), false, () => AddAction<PlayEffectAction>(property));

            menu.ShowAsContext();
        }

        private void AddAction<T>(SerializedProperty property) where T : UnitStatusAction, new()
        {
            property.arraySize++;
            var newElement = property.GetArrayElementAtIndex(property.arraySize - 1);
            var newAction = new T();

            // Set default values for TimeBasedActions
            if (newAction is TimeBasedAction timeBasedAction)
            {
                timeBasedAction.Duration = 1.0f;
                timeBasedAction.Delay = 0.0f;
            }

            // Set default values for specific action types
            if (newAction is PlayAnimationAction animAction)
            {
                animAction.AnimationName = "DefaultAnimation";
            }

            newElement.managedReferenceValue = newAction;
            property.serializedObject.ApplyModifiedProperties();

            // Select the newly created action
            _selectedActionIndex = property.arraySize - 1;
            _selectedPropertyPath = property.propertyPath;
        }
    }
}
#endif
