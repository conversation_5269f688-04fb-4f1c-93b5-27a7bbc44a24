#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript.Serialization;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Custom inspector for StatescriptAsset
    /// </summary>
    [CustomEditor(typeof(StatescriptAsset))]
    public class StatescriptAssetInspector : UnityEditor.Editor
    {
        private StatescriptAsset _asset;
        private bool _showStats = false;
        private bool _showValidation = false;

        private void OnEnable()
        {
            _asset = target as StatescriptAsset;
        }

        public override void OnInspectorGUI()
        {
            EditorGUILayout.Space();

            // Header
            EditorGUILayout.LabelField("Statescript Graph Asset", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Basic Info
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Graph Information", EditorStyles.boldLabel);

            EditorGUILayout.LabelField("Name", _asset.GraphName);
            EditorGUILayout.LabelField("Description", _asset.Description);
            EditorGUILayout.LabelField("Version", _asset.Version.ToString());
            EditorGUILayout.LabelField("Format", _asset.SerializationFormat.ToString());
            EditorGUILayout.LabelField("Data Size", _asset.HasData ? $"{_asset.DataSize} bytes" : "No data");

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            // Actions
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Create New Graph"))
            {
                if (EditorUtility.DisplayDialog("Create New Graph",
                    "This will replace the current graph data. Are you sure?",
                    "Yes", "Cancel"))
                {
                    _asset.CreateNewGraph();
                }
            }

            if (GUILayout.Button("Open Graph Editor"))
            {
                StatescriptGraphEditor.OpenAsset(_asset);
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Clear Cache"))
            {
                _asset.ClearCache();
                EditorUtility.DisplayDialog("Cache Cleared", "Graph cache has been cleared.", "OK");
            }

            if (GUILayout.Button("Validate Data"))
            {
                bool isValid = _asset.ValidateData();
                EditorUtility.DisplayDialog("Validation Result",
                    isValid ? "Graph data is valid." : "Graph data is invalid or corrupted.",
                    "OK");
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            // Serialization Format Conversion
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Serialization", EditorStyles.boldLabel);

            var newFormat = (StatescriptSerializationFormat)EditorGUILayout.EnumPopup("Format", _asset.SerializationFormat);
            if (newFormat != _asset.SerializationFormat)
            {
                if (EditorUtility.DisplayDialog("Change Format",
                    $"Convert from {_asset.SerializationFormat} to {newFormat}?",
                    "Yes", "Cancel"))
                {
                    _asset.ChangeSerializationFormat(newFormat);
                }
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();

            // Statistics (Foldout)
            _showStats = EditorGUILayout.Foldout(_showStats, "Serialization Statistics");
            if (_showStats)
            {
                EditorGUILayout.BeginVertical("box");

                if (_asset.HasData)
                {
                    var stats = _asset.GetSerializationStats();
                    if (string.IsNullOrEmpty(stats.Error))
                    {
                        EditorGUILayout.LabelField("JSON Size", $"{stats.JsonSize} bytes");
                        EditorGUILayout.LabelField("Binary Size", $"{stats.BinarySize} bytes");
                        EditorGUILayout.LabelField("Compressed Size", $"{stats.CompressedBinarySize} bytes");
                        EditorGUILayout.LabelField("Compression Ratio", $"{stats.CompressionRatio:F2}");

                        // Show efficiency
                        float jsonEfficiency = (float)stats.BinarySize / stats.JsonSize;
                        float compressionEfficiency = (float)stats.CompressedBinarySize / stats.BinarySize;

                        EditorGUILayout.Space();
                        EditorGUILayout.LabelField("Binary vs JSON", $"{jsonEfficiency:P1} size");
                        EditorGUILayout.LabelField("Compression Savings", $"{(1-compressionEfficiency):P1} reduction");
                    }
                    else
                    {
                        EditorGUILayout.LabelField("Error", stats.Error);
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("No data to analyze");
                }

                EditorGUILayout.EndVertical();
            }

            // Validation Details (Foldout)
            _showValidation = EditorGUILayout.Foldout(_showValidation, "Graph Validation");
            if (_showValidation)
            {
                EditorGUILayout.BeginVertical("box");

                if (_asset.HasData)
                {
                    var graph = _asset.LoadGraph();
                    if (graph != null)
                    {
                        var errors = graph.Validate();
                        if (errors.Count == 0)
                        {
                            EditorGUILayout.HelpBox("Graph is valid!", MessageType.Info);
                        }
                        else
                        {
                            EditorGUILayout.HelpBox($"Found {errors.Count} validation errors:", MessageType.Warning);
                            foreach (var error in errors)
                            {
                                EditorGUILayout.LabelField("• " + error, EditorStyles.wordWrappedLabel);
                            }
                        }

                        EditorGUILayout.Space();
                        EditorGUILayout.LabelField("Nodes", graph.Nodes.Count.ToString());
                        EditorGUILayout.LabelField("Connections", graph.Connections.Count.ToString());
                        EditorGUILayout.LabelField("Variables", graph.Variables.Count.ToString());
                    }
                    else
                    {
                        EditorGUILayout.HelpBox("Failed to load graph for validation", MessageType.Error);
                    }
                }
                else
                {
                    EditorGUILayout.HelpBox("No graph data to validate", MessageType.Warning);
                }

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.Space();

            // Help
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Help", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("• Use 'Create New Graph' to start with an empty graph", EditorStyles.wordWrappedLabel);
            EditorGUILayout.LabelField("• Binary format is most efficient for runtime", EditorStyles.wordWrappedLabel);
            EditorGUILayout.LabelField("• JSON format is human-readable for debugging", EditorStyles.wordWrappedLabel);
            EditorGUILayout.LabelField("• Compressed binary saves space for large graphs", EditorStyles.wordWrappedLabel);
            EditorGUILayout.EndVertical();
        }
    }
}
#endif
