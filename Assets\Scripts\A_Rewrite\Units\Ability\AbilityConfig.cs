using System;
using System.Collections.Generic;
using UnityEngine;

namespace Archery
{
    public enum eAbilityType
    {
        Active, // Active abilities that can be used by the player
        Passive // Passive abilities that are always active
    }

    public enum eTargetType : byte
    {
        Self, Enemy, Direction, Position
    }

    public enum eCastCostType : byte
    {
        None, // No cost
        Mana, // Cost in mana points
        Health, // Cost in health points
        Stamina // Cost in stamina points
    }

    public enum eDamageType
    {
        Physical,
        Magical,
        Pure
    }

    /// this is not what I want.
    [CreateAssetMenu(fileName = "AbilityConfig", menuName = "Scriptable Objects/AbilityConfig")]
    public class AbilityConfig : ScriptableObject
    {
        public string Name; // Ability name
        public string Description; // Ability description
        public Sprite Icon; // Ability icon

        public eAbilityType AbilityType; // Type of ability (Active or Passive)
        public eTargetType TargetType; // Type of target this ability can affect
        public eCastCostType CastCostType; // Type of cost for casting the ability
        public float Cooldown; // Cooldown time in seconds
        public float Duration; // Duration of the ability effect in seconds
        public float Cost;
    }

    [System.Serializable]
    abstract public class AbilityEffect 
    {
        public abstract void Execute(Unit caster, Unit target);
    }

    [System.Serializable]
    public class AnimEffect : AbilityEffect
    {
        public string AnimName;
        public override void Execute(Unit caster, Unit target)
        {
            if (target == null) return;

            // Play animation on the target
            //target.PlayAnimation(AnimName);
        }
    }

    [System.Serializable]
    public class DamageEffect : AbilityEffect
    {
        public eDamageType DamageType;
        public float DamageAmount; // Amount of damage to apply

        public override void Execute(Unit caster, Unit target)
        {
            if (target == null) return;

            // Apply damage to the target
            target.OnHurt(DamageType, DamageAmount);
        }
    }
}