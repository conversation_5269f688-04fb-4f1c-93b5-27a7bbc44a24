using System;
using System.Collections.Generic;
using UnityEngine;

namespace Archery
{
    public enum eStatusCategory : byte
    {
        Slot_1 = 0, // Example category, can be expanded
    }

    [CreateAssetMenu(fileName = "UnitStatusConfig", menuName = "Archery/UnitStatusConfig")]
    public class UnitStatusConfig : ScriptableObject
    {
        public string Name;
        public float Duration;
        public eStatusCategory Category;

        // List of actions that this status can perform
        [SerializeReference]
        public List<UnitStatusAction> Actions;
    }
}