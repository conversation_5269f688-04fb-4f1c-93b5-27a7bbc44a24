using System;
using System.Collections;
using System.Collections.Generic;
using GP.Common;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Archery
{
    public class DebugSpawnSystem : SystemBase
    {
        public UnitConfig unitConfig;

        public override void OnAwake()
        {
            base.OnAwake();
            GP.Pool.PoolAPI.RegisterFactory<Unit>(new GP.Pool.DelegatePoolFactory<Unit>(CreateUnit, DestroyUnit, OnUnitUsed, OnUnitReset));
        }

        public override void OnUpdate()
        {
            base.OnUpdate();

            if (Input.GetKeyDown(KeyCode.Space))
            {
                SpawnUnit();
            }
        }

        public override void OnReset()
        {
            base.OnReset();
        }

        private void SpawnUnit()
        {
            if (unitConfig == null)
            {
                Debug.LogError("UnitConfig is not assigned.");
                return;
            }

            var unit = GP.Pool.PoolAPI.Get<Unit>(unitConfig.prefabAddressable, true);
            if (unit != null)
            {
                OnUnitCreated(unit);
                return;
            }

            unitConfig.prefabAddressable.InstantiateAsync(null, false).Completed += handle =>
            {
                if (handle.Status == UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
                {
                    OnUnitCreated(handle.Result.GetComponent<Unit>());
                }
                else
                {
                    Debug.LogError("Failed to load unit prefab: " + handle.OperationException);
                }
            };
        }

        private Unit CreateUnit(object prefab)
        {
            if (prefab == null)
            {
                Debug.LogError("Prefab is null");
                return null;
            }

            var prefabAddressable = prefab as AssetReferenceGameObject;
            if (prefabAddressable == null)
            {
                Debug.LogError("Prefab is not an AssetReferenceGameObject");
                return null;
            }

            var op = prefabAddressable.InstantiateAsync(null, false);
            op.WaitForCompletion();
            if (op.Status != UnityEngine.ResourceManagement.AsyncOperations.AsyncOperationStatus.Succeeded)
            {
                Debug.LogError("Failed to instantiate prefab: " + op.OperationException);
                return null;
            }
            if (op.Result == null)
            {
                Debug.LogError("Result is null after instantiation");
                return null;
            }

            var unit = op.Result.GetComponent<Unit>();
            if (unit == null)
            {
                Debug.LogError("Failed to create unit from prefab");
                return null;
            }

            return unit;
        }

        private void DestroyUnit(Unit unit)
        {
            if (unit != null)
                Destroy(unit.gameObject);
        }

        private void OnUnitUsed(Unit unit)
        {
            if (unit != null)
            {
                unit.OnUse();
            }
        }

        private void OnUnitReset(Unit unit)
        {
            if (unit != null)
            {
                unit.OnReset();
            }
        }

        private void OnUnitCreated(Unit unit)
        {
            if (unit == null)
            {
                Debug.LogError("Unit is null");
                return;
            }

            unit.transform.SetPositionAndRotation(Vector3.zero, Quaternion.identity);

            unit.OnCreate(unitConfig);

            var moveComponent = unit.GetUnitComponent<MoveComponent>();
            if (moveComponent != null)
            {
                moveComponent.StartMoving(new Vector3(-10, 0, 0), () =>
                {
                    Debug.Log("Arrived at target position");
                });
            }
            else
            {
                var arrowMoveComponent = unit.GetUnitComponent<ArrowMoveComponent>();
                if (arrowMoveComponent != null)
                {
                    arrowMoveComponent.Launch(1.0f);
                }
            }
        }
    }
}