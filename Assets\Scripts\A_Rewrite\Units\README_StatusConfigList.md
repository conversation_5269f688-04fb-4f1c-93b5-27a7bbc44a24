# UnitStatusConfig List with Folder Drag-and-Drop

## Overview
This implementation provides a custom PropertyDrawer for `List<UnitStatusConfig>` fields that supports drag-and-drop functionality similar to Unity's SpriteAtlasV2 "Objects for Packing" feature.

## Features

### Drag-and-Drop Support
- **Folder Drag-and-Drop**: Drag entire folders containing UnitStatusConfig assets to automatically add all configs
- **Individual Asset Drag**: Drag individual UnitStatusConfig assets directly to the list
- **Visual Feedback**: Highlighted drop area with color changes during drag operations
- **Automatic Duplicate Prevention**: Prevents adding the same config multiple times

### Enhanced List Management
- **Smart Display**: Shows config name, duration, and action count for each item
- **Remove Duplicates**: Button to clean up any duplicate entries
- **Quick Actions**: Add, Clear, and Remove buttons for list management
- **Visual Indicators**: Dynamic text that changes during drag operations

### User Interface
- **Collapsible Header**: Foldout header showing the list title and item count
- **Contextual Buttons**: Buttons appear/disappear based on list state
- **Informative Labels**: Clear instructions and status information
- **Consistent Styling**: Matches Unity's native inspector appearance

## Usage

### Setting Up the Attribute
Add the `[UnitStatusConfigList]` attribute to any `List<UnitStatusConfig>` field:

```csharp
[UnitStatusConfigList("Available Status")]
public List<UnitStatusConfig> availableStatus;
```

### Drag-and-Drop Operations
1. **Folder Drop**: Drag a folder from the Project window to the list area
   - All UnitStatusConfig assets in the folder (and subfolders) will be added
   - Duplicates are automatically prevented

2. **Asset Drop**: Drag individual UnitStatusConfig assets to the list
   - Single or multiple assets can be dragged at once
   - Existing items won't be duplicated

### List Management
- **Add**: Manually add empty slots to the list
- **Clear**: Remove all items with confirmation dialog
- **Remove Dupes**: Clean up any duplicate entries
- **Remove Item**: Click the "×" button next to any item

## Testing

### Create Test Data
Use the provided menu items to create test scenarios:

1. **Full Test Setup**: `Archery > Test > Create Sample Unit with Status Folder`
   - Creates a test UnitConfig with empty status list
   - Creates a folder with sample UnitStatusConfig assets
   - Perfect for testing drag-and-drop functionality

2. **Status Folder Only**: `Archery > Test > Create Status Config Folder Only`
   - Creates just a folder with sample status configs
   - Choose your own location

3. **Clean Up**: `Archery > Test > Clean Test Data`
   - Removes all test data when you're done

### Testing Workflow
1. Run "Create Sample Unit with Status Folder"
2. Select the created TestArcher asset
3. In the inspector, find the "Available Status (0)" section
4. Drag the StatusConfigs folder to the list area
5. Watch as all status configs are automatically added!

## Technical Implementation

### Files Created
- `UnitStatusConfigListDrawer.cs`: Custom PropertyDrawer with drag-and-drop logic
- `UnitConfigTestMenu.cs`: Test menu items for creating sample data
- `UnitStatusConfigListAttribute`: Attribute class (in UnitConfig.cs)

### Key Features
- **Recursive Folder Search**: Finds all UnitStatusConfig assets in folders and subfolders
- **Duplicate Prevention**: Uses HashSet to prevent adding the same config twice
- **Visual Feedback**: Real-time UI updates during drag operations
- **Error Handling**: Graceful handling of invalid drops and edge cases

### Integration Points
- Works with Unity's native drag-and-drop system
- Integrates seamlessly with SerializedProperty system
- Maintains undo/redo functionality
- Supports multi-object editing

## Advanced Features

### Custom Display
Each list item shows:
- Asset name
- Duration in seconds
- Number of actions
- Example: "HurtStatus (0.5s, 2 actions)"

### Smart Buttons
- "Remove Dupes" only appears when there are multiple items
- Info text changes color and content during drag operations
- Confirmation dialogs for destructive operations

### Performance Optimizations
- Efficient duplicate checking using HashSet
- Minimal redraws during drag operations
- Lazy loading of asset information

## Future Enhancements
- Drag reordering within the list
- Bulk operations (select multiple items)
- Search/filter functionality
- Preview of status configs on hover
- Integration with the track-based status editor
- Export/import list configurations
