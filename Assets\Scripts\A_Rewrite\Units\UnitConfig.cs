using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;

namespace Archery
{
    // Material enums
    public enum ArrowMaterial
    {
        Wood,
        Iron,
        Steel,
        Magic
    }

    public enum TargetMaterial
    {
        Wood,
        Stone,
        Metal,
        Flesh
    }

    [CreateAssetMenu(fileName = "Unit", menuName = "Archery/UnitConfig")]
    public class UnitConfig : ScriptableObject
    {
        public int id; // ID
        public string unitName; // 名称
        public string description; // 描述
        public Sprite icon; // 图标
        public AssetReferenceGameObject prefabAddressable; // 预制体地址

        [Header("属性")]
        public int maxHp; // 最大生命值
        public int attack; // 攻击力
        public int attackRange; // 攻击范围
        public int attackSpeed; // 攻击速度
        public float speed; // 速度
        //public float radius; // 自身半径
        public float mass; // 自身质量

        [Common.FolderAssetsList(typeof(UnitStatusConfig), "Available Status")]
        public List<UnitStatusConfig> availableStatus; // 状态配置列表

        [Header("Abilities")]
        public List<AbilityConfig> availableAbilities; // 能力配置列表
    }

    [CreateAssetMenu(fileName = "Projectile", menuName = "Archery/ProjectileConfig")]
    public class ProjectileConfig : UnitConfig
    {
        public float damage;
    }
}