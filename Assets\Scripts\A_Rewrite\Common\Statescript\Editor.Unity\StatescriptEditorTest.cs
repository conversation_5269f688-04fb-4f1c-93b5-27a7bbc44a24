#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Test menu items for the enhanced Statescript editor
    /// </summary>
    public static class StatescriptEditorTest
    {
        [MenuItem("Statescript/Test/Create Test Graph with Variables")]
        public static void CreateTestGraphWithVariables()
        {
            // Create a new Statescript asset
            var asset = ScriptableObject.CreateInstance<StatescriptAsset>();
            var graph = new StatescriptGraph("Test Graph with Variables");

            // Add some test variables
            var healthVar = StatescriptVariable.CreateInt("Health", 100);
            healthVar.Description = "Player health points";
            healthVar.IsExposed = true;
            graph.Variables.Add(healthVar);

            var speedVar = StatescriptVariable.CreateFloat("Speed", 5.0f);
            speedVar.Description = "Movement speed";
            speedVar.IsExposed = true;
            graph.Variables.Add(speedVar);

            var playerNameVar = StatescriptVariable.CreateString("<PERSON>N<PERSON>", "Hero");
            playerNameVar.Description = "Player character name";
            playerNameVar.IsExposed = false;
            graph.Variables.Add(playerNameVar);

            var isAliveVar = StatescriptVariable.CreateBool("IsAlive", true);
            isAliveVar.Description = "Whether the player is alive";
            isAliveVar.IsExposed = true;
            graph.Variables.Add(isAliveVar);

            var positionVar = StatescriptVariable.CreateVector3("Position", Vector3.zero);
            positionVar.Description = "Player position in world";
            positionVar.IsExposed = false;
            graph.Variables.Add(positionVar);

            // Create some test nodes
            var entryNode = new EntryNode { Id = 1, Name = "Start" };
            var logNode = new LogNode { Id = 2, Name = "Log Health" };
            logNode.SetProperty("Message", "Player health: {Health}");

            var waitNode = new WaitNode { Id = 3, Name = "Wait 2 seconds" };
            waitNode.SetProperty("Duration", 2.0f);

            var getVarNode = new GetVariableNode { Id = 4, Name = "Get Health" };
            getVarNode.VariableName = "Health";

            var setVarNode = new SetVariableNode { Id = 5, Name = "Reduce Health" };
            setVarNode.VariableName = "Health";
            setVarNode.NewValue = StatescriptVariable.CreateInt("", 90).DefaultValue;

            // Add nodes to graph
            graph.AddNode(entryNode);
            graph.AddNode(logNode);
            graph.AddNode(waitNode);
            graph.AddNode(getVarNode);
            graph.AddNode(setVarNode);

            // Create connections
            var connection1 = new StatescriptConnection(1, 2, "Out", "In") { Id = 1 };
            var connection2 = new StatescriptConnection(2, 3, "Out", "In") { Id = 2 };
            var connection3 = new StatescriptConnection(3, 4, "Out", "In") { Id = 3 };
            var connection4 = new StatescriptConnection(4, 5, "Out", "In") { Id = 4 };

            graph.AddConnection(connection1);
            graph.AddConnection(connection2);
            graph.AddConnection(connection3);
            graph.AddConnection(connection4);

            // Create editor data
            var editorData = new StatescriptEditorData();

            // Set node positions
            editorData.SetNodeData(1, new NodeEditorData
            {
                NodeId = 1,
                Position = new Vector2(100, 100),
                Size = new Vector2(100, 50),
                Color = new Color(0.2f, 0.8f, 0.2f, 1.0f)
            });

            editorData.SetNodeData(2, new NodeEditorData
            {
                NodeId = 2,
                Position = new Vector2(250, 100),
                Size = new Vector2(120, 60),
                Color = new Color(0.2f, 0.6f, 1.0f, 1.0f)
            });

            editorData.SetNodeData(3, new NodeEditorData
            {
                NodeId = 3,
                Position = new Vector2(400, 100),
                Size = new Vector2(120, 60),
                Color = new Color(1.0f, 0.8f, 0.2f, 1.0f)
            });

            editorData.SetNodeData(4, new NodeEditorData
            {
                NodeId = 4,
                Position = new Vector2(250, 200),
                Size = new Vector2(110, 55),
                Color = new Color(0.8f, 0.2f, 1.0f, 1.0f)
            });

            editorData.SetNodeData(5, new NodeEditorData
            {
                NodeId = 5,
                Position = new Vector2(400, 200),
                Size = new Vector2(110, 55),
                Color = new Color(0.8f, 0.2f, 1.0f, 1.0f)
            });

            // Set connection visual properties
            editorData.SetConnectionData(1, new ConnectionEditorData
            {
                ConnectionId = 1,
                Color = Color.green,
                Width = 2f,
                Label = "Start",
                ShowLabel = true
            });

            editorData.SetConnectionData(2, new ConnectionEditorData
            {
                ConnectionId = 2,
                Color = Color.blue,
                Width = 2f,
                Label = "Log",
                ShowLabel = true
            });

            editorData.SetConnectionData(3, new ConnectionEditorData
            {
                ConnectionId = 3,
                Color = Color.yellow,
                Width = 2f,
                Label = "Wait",
                ShowLabel = true
            });

            editorData.SetConnectionData(4, new ConnectionEditorData
            {
                ConnectionId = 4,
                Color = Color.magenta,
                Width = 2f,
                Label = "Get->Set",
                ShowLabel = true
            });

            // Save the graph and editor data
            asset.SaveGraph(graph);
            asset.SetEditorData(editorData);

            // Create the asset file
            var path = "Assets/TestStatescriptGraphWithVariables.asset";
            AssetDatabase.CreateAsset(asset, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Select and ping the asset
            Selection.activeObject = asset;
            EditorGUIUtility.PingObject(asset);

            Debug.Log($"Created test Statescript graph with variables at: {path}");
            Debug.Log($"Graph contains {graph.Variables.Count} variables and {graph.Nodes.Count} nodes");

            // Open the graph editor
            StatescriptGraphEditor.OpenAsset(asset);
        }

        [MenuItem("Statescript/Test/Open Enhanced Graph Editor")]
        public static void OpenEnhancedGraphEditor()
        {
            StatescriptGraphEditor.OpenWindow();
        }

        [MenuItem("Statescript/Test/Create Complex Connection Test")]
        public static void CreateComplexConnectionTest()
        {
            var asset = ScriptableObject.CreateInstance<StatescriptAsset>();
            var graph = new StatescriptGraph("Connection Test Graph");

            // Create nodes in a more complex layout
            var entryNode = new EntryNode { Id = 1, Name = "Entry" };
            var branchNode = new BranchNode { Id = 2, Name = "Health Check" };
            var logNode1 = new LogNode { Id = 3, Name = "Healthy" };
            var logNode2 = new LogNode { Id = 4, Name = "Injured" };
            var waitNode = new WaitNode { Id = 5, Name = "Wait" };

            branchNode.SetProperty("Condition", true);
            logNode1.SetProperty("Message", "Player is healthy!");
            logNode2.SetProperty("Message", "Player needs healing!");
            waitNode.SetProperty("Duration", 1.0f);

            graph.AddNode(entryNode);
            graph.AddNode(branchNode);
            graph.AddNode(logNode1);
            graph.AddNode(logNode2);
            graph.AddNode(waitNode);

            // Create connections with different properties
            var conn1 = new StatescriptConnection(1, 2, "Out", "In") { Id = 1 };
            var conn2 = new StatescriptConnection(2, 3, "Success", "In") { Id = 2 };
            var conn3 = new StatescriptConnection(2, 4, "Failure", "In") { Id = 3 };
            var conn4 = new StatescriptConnection(3, 5, "Out", "In") { Id = 4 };
            var conn5 = new StatescriptConnection(4, 5, "Out", "In") { Id = 5 };

            // Add custom properties to connections
            conn2.SetProperty("Type", "Success");
            conn2.SetProperty("Priority", 1);
            conn3.SetProperty("Type", "Failure");
            conn3.SetProperty("Priority", 2);

            graph.AddConnection(conn1);
            graph.AddConnection(conn2);
            graph.AddConnection(conn3);
            graph.AddConnection(conn4);
            graph.AddConnection(conn5);

            // Create editor data with custom connection visuals
            var editorData = new StatescriptEditorData();

            // Position nodes
            editorData.SetNodeData(1, new NodeEditorData { NodeId = 1, Position = new Vector2(100, 150), Size = new Vector2(100, 50) });
            editorData.SetNodeData(2, new NodeEditorData { NodeId = 2, Position = new Vector2(300, 150), Size = new Vector2(120, 60) });
            editorData.SetNodeData(3, new NodeEditorData { NodeId = 3, Position = new Vector2(500, 100), Size = new Vector2(100, 50) });
            editorData.SetNodeData(4, new NodeEditorData { NodeId = 4, Position = new Vector2(500, 200), Size = new Vector2(100, 50) });
            editorData.SetNodeData(5, new NodeEditorData { NodeId = 5, Position = new Vector2(700, 150), Size = new Vector2(100, 50) });

            // Style connections differently
            editorData.SetConnectionData(1, new ConnectionEditorData { ConnectionId = 1, Color = Color.white, Width = 2f });
            editorData.SetConnectionData(2, new ConnectionEditorData { ConnectionId = 2, Color = Color.green, Width = 3f, Label = "TRUE", ShowLabel = true });
            editorData.SetConnectionData(3, new ConnectionEditorData { ConnectionId = 3, Color = Color.red, Width = 3f, Label = "FALSE", ShowLabel = true });
            editorData.SetConnectionData(4, new ConnectionEditorData { ConnectionId = 4, Color = Color.cyan, Width = 2f });
            editorData.SetConnectionData(5, new ConnectionEditorData { ConnectionId = 5, Color = Color.magenta, Width = 2f });

            asset.SaveGraph(graph);
            asset.SetEditorData(editorData);

            var path = "Assets/TestComplexConnections.asset";
            AssetDatabase.CreateAsset(asset, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            Selection.activeObject = asset;
            EditorGUIUtility.PingObject(asset);

            Debug.Log($"Created complex connection test at: {path}");
            StatescriptGraphEditor.OpenAsset(asset);
        }
    }
}
#endif
