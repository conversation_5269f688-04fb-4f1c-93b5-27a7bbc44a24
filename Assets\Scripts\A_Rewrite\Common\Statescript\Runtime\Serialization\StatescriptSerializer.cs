using System;
using System.IO;
using System.Text;
using System.Text.Json;
using MemoryPack;

namespace GP.Common.Statescript.Serialization
{
    /// <summary>
    /// Serialization formats supported by the Statescript system
    /// </summary>
    public enum StatescriptSerializationFormat
    {
        Json,
        Binary,
        CompressedBinary
    }

    /// <summary>
    /// Handles serialization and deserialization of Statescript graphs
    /// </summary>
    public static class StatescriptSerializer
    {
        /// <summary>
        /// Serialize a graph to the specified format
        /// </summary>
        public static byte[] Serialize(StatescriptGraph graph, StatescriptSerializationFormat format = StatescriptSerializationFormat.Binary)
        {
            if (graph == null)
                throw new ArgumentNullException(nameof(graph));

            return format switch
            {
                StatescriptSerializationFormat.Json => SerializeToJson(graph),
                StatescriptSerializationFormat.Binary => SerializeToBinary(graph),
                StatescriptSerializationFormat.CompressedBinary => SerializeToCompressedBinary(graph),
                _ => throw new ArgumentException($"Unsupported serialization format: {format}")
            };
        }

        /// <summary>
        /// Deserialize a graph from the specified format
        /// </summary>
        public static StatescriptGraph Deserialize(byte[] data, StatescriptSerializationFormat format = StatescriptSerializationFormat.Binary)
        {
            if (data == null || data.Length == 0)
                throw new ArgumentException("Data cannot be null or empty");

            return format switch
            {
                StatescriptSerializationFormat.Json => DeserializeFromJson(data),
                StatescriptSerializationFormat.Binary => DeserializeFromBinary(data),
                StatescriptSerializationFormat.CompressedBinary => DeserializeFromCompressedBinary(data),
                _ => throw new ArgumentException($"Unsupported serialization format: {format}")
            };
        }

        /// <summary>
        /// Serialize a graph to a file
        /// </summary>
        public static void SerializeToFile(StatescriptGraph graph, string filePath, StatescriptSerializationFormat format = StatescriptSerializationFormat.Binary)
        {
            var data = Serialize(graph, format);
            File.WriteAllBytes(filePath, data);
        }

        /// <summary>
        /// Deserialize a graph from a file
        /// </summary>
        public static StatescriptGraph DeserializeFromFile(string filePath, StatescriptSerializationFormat format = StatescriptSerializationFormat.Binary)
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"File not found: {filePath}");

            var data = File.ReadAllBytes(filePath);
            return Deserialize(data, format);
        }

        /// <summary>
        /// Auto-detect the format based on file extension
        /// </summary>
        public static StatescriptSerializationFormat DetectFormat(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".json" => StatescriptSerializationFormat.Json,
                ".bin" => StatescriptSerializationFormat.Binary,
                ".cbz" or ".compressed" => StatescriptSerializationFormat.CompressedBinary,
                _ => StatescriptSerializationFormat.Binary
            };
        }

        /// <summary>
        /// Serialize and deserialize from file with auto-format detection
        /// </summary>
        public static void SerializeToFileAuto(StatescriptGraph graph, string filePath)
        {
            var format = DetectFormat(filePath);
            SerializeToFile(graph, filePath, format);
        }

        /// <summary>
        /// Deserialize from file with auto-format detection
        /// </summary>
        public static StatescriptGraph DeserializeFromFileAuto(string filePath)
        {
            var format = DetectFormat(filePath);
            return DeserializeFromFile(filePath, format);
        }

        #region JSON Serialization

        private static byte[] SerializeToJson(StatescriptGraph graph)
        {
            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                IncludeFields = true
            };

            var json = JsonSerializer.Serialize(graph, options);
            return Encoding.UTF8.GetBytes(json);
        }

        private static StatescriptGraph DeserializeFromJson(byte[] data)
        {
            var json = Encoding.UTF8.GetString(data);
            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                IncludeFields = true
            };

            return JsonSerializer.Deserialize<StatescriptGraph>(json, options);
        }

        #endregion

        #region Binary Serialization (MemoryPack)

        private static byte[] SerializeToBinary(StatescriptGraph graph)
        {
            return MemoryPackSerializer.Serialize(graph);
        }

        private static StatescriptGraph DeserializeFromBinary(byte[] data)
        {
            return MemoryPackSerializer.Deserialize<StatescriptGraph>(data);
        }

        #endregion

        #region Compressed Binary Serialization

        private static byte[] SerializeToCompressedBinary(StatescriptGraph graph)
        {
            var binaryData = SerializeToBinary(graph);
            
            using var output = new MemoryStream();
            using var compressionStream = new System.IO.Compression.GZipStream(output, System.IO.Compression.CompressionMode.Compress);
            compressionStream.Write(binaryData, 0, binaryData.Length);
            compressionStream.Close();
            
            return output.ToArray();
        }

        private static StatescriptGraph DeserializeFromCompressedBinary(byte[] data)
        {
            using var input = new MemoryStream(data);
            using var decompressionStream = new System.IO.Compression.GZipStream(input, System.IO.Compression.CompressionMode.Decompress);
            using var output = new MemoryStream();
            
            decompressionStream.CopyTo(output);
            var decompressedData = output.ToArray();
            
            return DeserializeFromBinary(decompressedData);
        }

        #endregion

        #region Validation and Utilities

        /// <summary>
        /// Validate that serialized data can be properly deserialized
        /// </summary>
        public static bool ValidateSerializedData(byte[] data, StatescriptSerializationFormat format)
        {
            try
            {
                var graph = Deserialize(data, format);
                return graph != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get the size of serialized data for different formats
        /// </summary>
        public static SerializationStats GetSerializationStats(StatescriptGraph graph)
        {
            var stats = new SerializationStats();
            
            try
            {
                var jsonData = SerializeToJson(graph);
                stats.JsonSize = jsonData.Length;
                
                var binaryData = SerializeToBinary(graph);
                stats.BinarySize = binaryData.Length;
                
                var compressedData = SerializeToCompressedBinary(graph);
                stats.CompressedBinarySize = compressedData.Length;
                
                stats.CompressionRatio = (float)stats.CompressedBinarySize / stats.BinarySize;
            }
            catch (Exception ex)
            {
                stats.Error = ex.Message;
            }
            
            return stats;
        }

        #endregion
    }

    /// <summary>
    /// Statistics about serialization performance
    /// </summary>
    public class SerializationStats
    {
        public int JsonSize { get; set; }
        public int BinarySize { get; set; }
        public int CompressedBinarySize { get; set; }
        public float CompressionRatio { get; set; }
        public string Error { get; set; }

        public override string ToString()
        {
            if (!string.IsNullOrEmpty(Error))
                return $"Error: {Error}";

            return $"JSON: {JsonSize} bytes, Binary: {BinarySize} bytes, Compressed: {CompressedBinarySize} bytes (Ratio: {CompressionRatio:F2})";
        }
    }
}
