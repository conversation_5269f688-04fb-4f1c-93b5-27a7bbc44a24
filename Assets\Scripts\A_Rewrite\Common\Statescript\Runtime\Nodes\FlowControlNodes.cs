using System.Collections.Generic;
using System.Linq;
using System;
using MemoryPack;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Executes multiple nodes in parallel
    /// </summary>
    [MemoryPackable]
    public sealed partial class ParallelNode : StatescriptNode
    {
        [MemoryPackInclude]
        public ParallelExecutionMode ExecutionMode { get; set; } = ParallelExecutionMode.WaitForAll;

        [MemoryPackIgnore]
        private HashSet<int> _completedNodes = new();
        
        [MemoryPackIgnore]
        private HashSet<int> _runningNodes = new();

        public ParallelNode() : base(StatescriptNodeType.FlowControl)
        {
            Name = "Parallel";
        }

        protected override void OnExecute()
        {
            _completedNodes.Clear();
            _runningNodes.Clear();

            var connections = Graph?.GetOutgoingConnections(Id);
            if (connections == null || connections.Count == 0)
            {
                CompleteExecution(true);
                return;
            }

            // Start all connected nodes
            foreach (var connection in connections)
            {
                var nextNode = Graph.GetNode(connection.ToNodeId);
                if (nextNode != null)
                {
                    _runningNodes.Add(nextNode.Id);
                    nextNode.Execute();
                }
            }

            SetWaiting();
        }

        protected override void OnUpdate(float deltaTime)
        {
            if (State != StatescriptNodeState.Waiting) return;

            // Check completion status of running nodes
            var nodesToRemove = new List<int>();
            foreach (var nodeId in _runningNodes)
            {
                var node = Graph.GetNode(nodeId);
                if (node != null && !node.IsActive)
                {
                    _completedNodes.Add(nodeId);
                    nodesToRemove.Add(nodeId);
                }
            }

            foreach (var nodeId in nodesToRemove)
            {
                _runningNodes.Remove(nodeId);
            }

            // Check if we should complete based on execution mode
            bool shouldComplete = ExecutionMode switch
            {
                ParallelExecutionMode.WaitForAll => _runningNodes.Count == 0,
                ParallelExecutionMode.WaitForAny => _completedNodes.Count > 0,
                ParallelExecutionMode.WaitForMajority => _completedNodes.Count > (_completedNodes.Count + _runningNodes.Count) / 2,
                _ => false
            };

            if (shouldComplete)
            {
                ResumeFromWaiting(true);
            }
        }

        protected override void OnReset()
        {
            _completedNodes.Clear();
            _runningNodes.Clear();
        }

        protected override void OnExecutionComplete(bool success)
        {
            // Parallel node doesn't trigger connections automatically
            // The individual parallel branches handle their own flow
        }
    }

    /// <summary>
    /// Execution modes for parallel nodes
    /// </summary>
    public enum ParallelExecutionMode
    {
        WaitForAll,     // Wait for all parallel branches to complete
        WaitForAny,     // Complete when any branch completes
        WaitForMajority // Complete when majority of branches complete
    }

    /// <summary>
    /// Loops execution of connected nodes
    /// </summary>
    [MemoryPackable]
    public sealed partial class LoopNode : StatescriptNode
    {
        [MemoryPackInclude]
        public LoopType Type { get; set; } = LoopType.Count;
        
        [MemoryPackInclude]
        public int MaxIterations { get; set; } = 1;
        
        [MemoryPackInclude]
        public string ConditionVariable { get; set; } = string.Empty;

        [MemoryPackIgnore]
        private int _currentIteration;

        public LoopNode() : base(StatescriptNodeType.FlowControl)
        {
            Name = "Loop";
        }

        protected override void OnExecute()
        {
            _currentIteration = 0;
            ExecuteLoop();
        }

        protected override void OnReset()
        {
            _currentIteration = 0;
        }

        private void ExecuteLoop()
        {
            if (!ShouldContinueLoop())
            {
                CompleteExecution(true);
                return;
            }

            _currentIteration++;
            
            // Execute the loop body
            var connections = Graph?.GetOutgoingConnections(Id);
            var bodyConnection = connections?.FirstOrDefault(c => c.GetProperty<string>("Type", "Body") == "Body");
            
            if (bodyConnection != null)
            {
                var bodyNode = Graph.GetNode(bodyConnection.ToNodeId);
                bodyNode?.Execute();
                SetWaiting();
            }
            else
            {
                CompleteExecution(false);
            }
        }

        protected override void OnUpdate(float deltaTime)
        {
            if (State != StatescriptNodeState.Waiting) return;

            // Check if loop body has completed
            var connections = Graph?.GetOutgoingConnections(Id);
            var bodyConnection = connections?.FirstOrDefault(c => c.GetProperty<string>("Type", "Body") == "Body");
            
            if (bodyConnection != null)
            {
                var bodyNode = Graph.GetNode(bodyConnection.ToNodeId);
                if (bodyNode != null && !bodyNode.IsActive)
                {
                    // Body completed, continue loop
                    ExecuteLoop();
                }
            }
        }

        private bool ShouldContinueLoop()
        {
            return Type switch
            {
                LoopType.Count => _currentIteration < MaxIterations,
                LoopType.While => EvaluateCondition(),
                LoopType.Infinite => true,
                _ => false
            };
        }

        private bool EvaluateCondition()
        {
            if (string.IsNullOrEmpty(ConditionVariable))
                return false;

            return Graph?.GetVariableValue<bool>(ConditionVariable, false) ?? false;
        }

        protected override void OnExecutionComplete(bool success)
        {
            // Trigger exit connections
            var connections = Graph?.GetOutgoingConnections(Id);
            var exitConnections = connections?.Where(c => c.GetProperty<string>("Type", "Exit") == "Exit");
            
            if (exitConnections != null)
            {
                foreach (var connection in exitConnections)
                {
                    var nextNode = Graph.GetNode(connection.ToNodeId);
                    nextNode?.Execute();
                }
            }
        }
    }

    /// <summary>
    /// Loop types
    /// </summary>
    public enum LoopType
    {
        Count,      // Loop a specific number of times
        While,      // Loop while a condition is true
        Infinite    // Loop forever (until manually broken)
    }

    /// <summary>
    /// Evaluates conditions and branches execution
    /// </summary>
    [MemoryPackable]
    public sealed partial class ConditionNode : StatescriptNode
    {
        [MemoryPackInclude]
        public ConditionType Type { get; set; } = ConditionType.Variable;
        
        [MemoryPackInclude]
        public string LeftOperand { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public string RightOperand { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public ComparisonOperator Operator { get; set; } = ComparisonOperator.Equal;

        public ConditionNode() : base(StatescriptNodeType.Condition)
        {
            Name = "Condition";
        }

        protected override void OnExecute()
        {
            bool result = EvaluateCondition();
            CompleteExecution(result);
        }

        protected override bool ShouldTriggerConnection(StatescriptConnection connection, bool success)
        {
            var connectionType = connection.GetProperty<string>("Type", "True");
            return (success && connectionType == "True") || (!success && connectionType == "False");
        }

        private bool EvaluateCondition()
        {
            return Type switch
            {
                ConditionType.Variable => EvaluateVariableCondition(),
                ConditionType.EntityProperty => EvaluateEntityPropertyCondition(),
                ConditionType.Custom => EvaluateCustomCondition(),
                _ => false
            };
        }

        private bool EvaluateVariableCondition()
        {
            var leftValue = Graph?.GetVariable(LeftOperand)?.GetValueAsObject();
            var rightValue = GetRightOperandValue();

            if (leftValue == null || rightValue == null)
                return false;

            return CompareValues(leftValue, rightValue, Operator);
        }

        private bool EvaluateEntityPropertyCondition()
        {
            // This would evaluate properties of the target entity
            // Implementation depends on your entity system
            return false;
        }

        private bool EvaluateCustomCondition()
        {
            // This would allow for custom condition evaluation
            // Could be implemented via delegates or scripting
            return GetProperty<bool>("CustomResult", false);
        }

        private object GetRightOperandValue()
        {
            // Try to parse as different types
            if (bool.TryParse(RightOperand, out bool boolValue))
                return boolValue;
            
            if (int.TryParse(RightOperand, out int intValue))
                return intValue;
            
            if (float.TryParse(RightOperand, out float floatValue))
                return floatValue;

            // Check if it's a variable reference
            if (RightOperand.StartsWith("$"))
            {
                var variableName = RightOperand.Substring(1);
                return Graph?.GetVariable(variableName)?.GetValueAsObject();
            }

            // Return as string
            return RightOperand;
        }

        private bool CompareValues(object left, object right, ComparisonOperator op)
        {
            // Convert to comparable types if possible
            if (left is IComparable leftComparable && right is IComparable rightComparable)
            {
                try
                {
                    int comparison = leftComparable.CompareTo(right);
                    return op switch
                    {
                        ComparisonOperator.Equal => comparison == 0,
                        ComparisonOperator.NotEqual => comparison != 0,
                        ComparisonOperator.Greater => comparison > 0,
                        ComparisonOperator.GreaterEqual => comparison >= 0,
                        ComparisonOperator.Less => comparison < 0,
                        ComparisonOperator.LessEqual => comparison <= 0,
                        _ => false
                    };
                }
                catch
                {
                    // Comparison failed, fall back to equality
                    return op == ComparisonOperator.Equal ? left.Equals(right) : !left.Equals(right);
                }
            }

            // Fall back to equality comparison
            return op == ComparisonOperator.Equal ? left.Equals(right) : !left.Equals(right);
        }
    }

    /// <summary>
    /// Condition types
    /// </summary>
    public enum ConditionType
    {
        Variable,       // Compare variables
        EntityProperty, // Compare entity properties
        Custom          // Custom condition logic
    }

    /// <summary>
    /// Comparison operators
    /// </summary>
    public enum ComparisonOperator
    {
        Equal,
        NotEqual,
        Greater,
        GreaterEqual,
        Less,
        LessEqual
    }
}
