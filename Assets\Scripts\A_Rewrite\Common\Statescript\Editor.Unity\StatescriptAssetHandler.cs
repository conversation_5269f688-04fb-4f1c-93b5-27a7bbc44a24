#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEditor.Callbacks;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Handles opening StatescriptAssets in the graph editor
    /// </summary>
    public static class StatescriptAssetHandler
    {
        [OnOpenAsset]
        public static bool OnOpenAsset(int instanceID, int line)
        {
            var asset = EditorUtility.InstanceIDToObject(instanceID) as StatescriptAsset;
            if (asset != null)
            {
                StatescriptGraphEditor.OpenAsset(asset);
                return true;
            }
            return false;
        }
    }
}
#endif
