#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Panel for managing variables in a Statescript graph
    /// </summary>
    public class StatescriptVariablePanel
    {
        private StatescriptGraph _currentGraph;
        private Vector2 _scrollPosition;
        private bool _showAddVariableSection = false;
        private string _newVariableName = "";
        private VariableType _newVariableType = VariableType.Integer;
        private object _newVariableDefaultValue = 0;
        private bool _newVariableIsExposed = false;
        private string _newVariableDescription = "";

        // Variable editing
        private Dictionary<string, bool> _editingVariables = new();
        private Dictionary<string, object> _editingValues = new();

        public enum VariableType
        {
            Boolean,
            Integer,
            Float,
            String,
            Vector2,
            Vector3,
            Entity,
            Object
        }

        public void SetGraph(StatescriptGraph graph)
        {
            _currentGraph = graph;
            _editingVariables.Clear();
            _editingValues.Clear();
        }

        public void OnGUI(Rect rect)
        {
            if (_currentGraph == null)
            {
                EditorGUI.HelpBox(rect, "No graph loaded", MessageType.Info);
                return;
            }

            GUILayout.BeginArea(rect);
            
            // Header
            GUILayout.BeginHorizontal(EditorStyles.toolbar);
            GUILayout.Label("Variables", EditorStyles.boldLabel);
            GUILayout.FlexibleSpace();
            
            if (GUILayout.Button("Add Variable", EditorStyles.toolbarButton))
            {
                _showAddVariableSection = !_showAddVariableSection;
            }
            GUILayout.EndHorizontal();

            _scrollPosition = GUILayout.BeginScrollView(_scrollPosition);

            // Add variable section
            if (_showAddVariableSection)
            {
                DrawAddVariableSection();
                GUILayout.Space(10);
            }

            // Existing variables
            DrawExistingVariables();

            GUILayout.EndScrollView();
            GUILayout.EndArea();
        }

        private void DrawAddVariableSection()
        {
            GUILayout.BeginVertical(GUI.skin.box);
            GUILayout.Label("Add New Variable", EditorStyles.boldLabel);

            // Variable name
            GUILayout.BeginHorizontal();
            GUILayout.Label("Name:", GUILayout.Width(80));
            _newVariableName = GUILayout.TextField(_newVariableName);
            GUILayout.EndHorizontal();

            // Variable type
            GUILayout.BeginHorizontal();
            GUILayout.Label("Type:", GUILayout.Width(80));
            _newVariableType = (VariableType)EditorGUILayout.EnumPopup(_newVariableType);
            GUILayout.EndHorizontal();

            // Default value
            GUILayout.BeginHorizontal();
            GUILayout.Label("Default:", GUILayout.Width(80));
            _newVariableDefaultValue = DrawVariableValueField(_newVariableType, _newVariableDefaultValue);
            GUILayout.EndHorizontal();

            // Description
            GUILayout.BeginHorizontal();
            GUILayout.Label("Description:", GUILayout.Width(80));
            _newVariableDescription = GUILayout.TextField(_newVariableDescription);
            GUILayout.EndHorizontal();

            // Is exposed
            GUILayout.BeginHorizontal();
            GUILayout.Label("Exposed:", GUILayout.Width(80));
            _newVariableIsExposed = GUILayout.Toggle(_newVariableIsExposed, "");
            GUILayout.EndHorizontal();

            GUILayout.Space(5);

            // Buttons
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Create Variable"))
            {
                CreateVariable();
            }
            if (GUILayout.Button("Cancel"))
            {
                _showAddVariableSection = false;
                ResetNewVariableFields();
            }
            GUILayout.EndHorizontal();

            GUILayout.EndVertical();
        }

        private void DrawExistingVariables()
        {
            if (_currentGraph.Variables.Count == 0)
            {
                GUILayout.Label("No variables defined", EditorStyles.centeredGreyMiniLabel);
                return;
            }

            var variablesToRemove = new List<StatescriptVariable>();

            foreach (var variable in _currentGraph.Variables)
            {
                GUILayout.BeginVertical(GUI.skin.box);
                
                GUILayout.BeginHorizontal();
                
                // Variable name and type
                var isEditing = _editingVariables.GetValueOrDefault(variable.Name, false);
                
                if (!isEditing)
                {
                    GUILayout.Label($"{variable.Name} ({GetVariableTypeName(variable.DefaultValue)})", EditorStyles.boldLabel);
                    GUILayout.FlexibleSpace();
                    
                    if (GUILayout.Button("Edit", GUILayout.Width(40)))
                    {
                        StartEditingVariable(variable);
                    }
                    
                    if (GUILayout.Button("X", GUILayout.Width(20)))
                    {
                        if (EditorUtility.DisplayDialog("Delete Variable", 
                            $"Are you sure you want to delete variable '{variable.Name}'?", 
                            "Delete", "Cancel"))
                        {
                            variablesToRemove.Add(variable);
                        }
                    }
                }
                else
                {
                    // Edit mode
                    DrawVariableEditMode(variable);
                }
                
                GUILayout.EndHorizontal();

                if (!isEditing)
                {
                    // Show variable info
                    if (!string.IsNullOrEmpty(variable.Description))
                    {
                        GUILayout.Label($"Description: {variable.Description}", EditorStyles.miniLabel);
                    }
                    
                    GUILayout.Label($"Default Value: {GetVariableValueString(variable.DefaultValue)}", EditorStyles.miniLabel);
                    GUILayout.Label($"Exposed: {variable.IsExposed}", EditorStyles.miniLabel);
                }

                GUILayout.EndVertical();
                GUILayout.Space(2);
            }

            // Remove variables marked for deletion
            foreach (var variable in variablesToRemove)
            {
                _currentGraph.Variables.Remove(variable);
                _editingVariables.Remove(variable.Name);
                _editingValues.Remove(variable.Name);
            }
        }

        private void DrawVariableEditMode(StatescriptVariable variable)
        {
            GUILayout.BeginVertical();
            
            // Name (read-only for now to avoid reference issues)
            GUILayout.BeginHorizontal();
            GUILayout.Label("Name:", GUILayout.Width(80));
            GUILayout.Label(variable.Name);
            GUILayout.EndHorizontal();

            // Description
            GUILayout.BeginHorizontal();
            GUILayout.Label("Description:", GUILayout.Width(80));
            variable.Description = GUILayout.TextField(variable.Description);
            GUILayout.EndHorizontal();

            // Default value
            GUILayout.BeginHorizontal();
            GUILayout.Label("Default:", GUILayout.Width(80));
            var newValue = DrawVariableValueEditField(variable.DefaultValue);
            if (newValue != null)
            {
                variable.DefaultValue = newValue;
            }
            GUILayout.EndHorizontal();

            // Is exposed
            GUILayout.BeginHorizontal();
            GUILayout.Label("Exposed:", GUILayout.Width(80));
            variable.IsExposed = GUILayout.Toggle(variable.IsExposed, "");
            GUILayout.EndHorizontal();

            // Buttons
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("Save", GUILayout.Width(50)))
            {
                StopEditingVariable(variable.Name);
            }
            if (GUILayout.Button("Cancel", GUILayout.Width(50)))
            {
                StopEditingVariable(variable.Name);
            }
            GUILayout.EndHorizontal();

            GUILayout.EndVertical();
        }

        private object DrawVariableValueField(VariableType type, object currentValue)
        {
            switch (type)
            {
                case VariableType.Boolean:
                    return EditorGUILayout.Toggle(currentValue is bool b ? b : false);
                case VariableType.Integer:
                    return EditorGUILayout.IntField(currentValue is int i ? i : 0);
                case VariableType.Float:
                    return EditorGUILayout.FloatField(currentValue is float f ? f : 0f);
                case VariableType.String:
                    return EditorGUILayout.TextField(currentValue as string ?? "");
                case VariableType.Vector2:
                    return EditorGUILayout.Vector2Field("", currentValue is Vector2 v2 ? v2 : Vector2.zero);
                case VariableType.Vector3:
                    return EditorGUILayout.Vector3Field("", currentValue is Vector3 v3 ? v3 : Vector3.zero);
                case VariableType.Entity:
                case VariableType.Object:
                    return EditorGUILayout.ObjectField(currentValue as UnityEngine.Object, typeof(UnityEngine.Object), true);
                default:
                    return currentValue;
            }
        }

        private IVariable DrawVariableValueEditField(IVariable variable)
        {
            if (variable == null) return null;

            var type = variable.GetType();
            if (!type.IsGenericType || type.GetGenericTypeDefinition() != typeof(Variable<>))
                return variable;

            var genericArg = type.GetGenericArguments()[0];
            var valueProperty = type.GetProperty("Value");
            var currentValue = valueProperty?.GetValue(variable);

            object newValue = null;
            if (genericArg == typeof(bool))
                newValue = EditorGUILayout.Toggle(currentValue is bool b ? b : false);
            else if (genericArg == typeof(int))
                newValue = EditorGUILayout.IntField(currentValue is int i ? i : 0);
            else if (genericArg == typeof(float))
                newValue = EditorGUILayout.FloatField(currentValue is float f ? f : 0f);
            else if (genericArg == typeof(string))
                newValue = EditorGUILayout.TextField(currentValue as string ?? "");
            else if (genericArg == typeof(Vector2))
                newValue = EditorGUILayout.Vector2Field("", currentValue is Vector2 v2 ? v2 : Vector2.zero);
            else if (genericArg == typeof(Vector3))
                newValue = EditorGUILayout.Vector3Field("", currentValue is Vector3 v3 ? v3 : Vector3.zero);
            else
                return variable;

            if (newValue != null && !newValue.Equals(currentValue))
            {
                var variableType = typeof(Variable<>).MakeGenericType(genericArg);
                return (IVariable)Activator.CreateInstance(variableType, newValue);
            }

            return variable;
        }

        private void CreateVariable()
        {
            if (string.IsNullOrWhiteSpace(_newVariableName))
            {
                EditorUtility.DisplayDialog("Invalid Name", "Variable name cannot be empty.", "OK");
                return;
            }

            if (_currentGraph.Variables.Any(v => v.Name == _newVariableName))
            {
                EditorUtility.DisplayDialog("Duplicate Name", "A variable with this name already exists.", "OK");
                return;
            }

            var variable = CreateVariableOfType(_newVariableType, _newVariableName, _newVariableDefaultValue);
            variable.Description = _newVariableDescription;
            variable.IsExposed = _newVariableIsExposed;

            _currentGraph.Variables.Add(variable);

            _showAddVariableSection = false;
            ResetNewVariableFields();
        }

        private StatescriptVariable CreateVariableOfType(VariableType type, string name, object defaultValue)
        {
            switch (type)
            {
                case VariableType.Boolean:
                    return StatescriptVariable.CreateBool(name, defaultValue is bool b ? b : false);
                case VariableType.Integer:
                    return StatescriptVariable.CreateInt(name, defaultValue is int i ? i : 0);
                case VariableType.Float:
                    return StatescriptVariable.CreateFloat(name, defaultValue is float f ? f : 0f);
                case VariableType.String:
                    return StatescriptVariable.CreateString(name, defaultValue as string ?? "");
                case VariableType.Vector2:
                    return StatescriptVariable.CreateVector2(name, defaultValue is Vector2 v2 ? v2 : Vector2.zero);
                case VariableType.Vector3:
                    return StatescriptVariable.CreateVector3(name, defaultValue is Vector3 v3 ? v3 : Vector3.zero);
                default:
                    return StatescriptVariable.CreateInt(name, 0);
            }
        }

        private void StartEditingVariable(StatescriptVariable variable)
        {
            _editingVariables[variable.Name] = true;
        }

        private void StopEditingVariable(string variableName)
        {
            _editingVariables[variableName] = false;
            _editingValues.Remove(variableName);
        }

        private void ResetNewVariableFields()
        {
            _newVariableName = "";
            _newVariableType = VariableType.Integer;
            _newVariableDefaultValue = 0;
            _newVariableIsExposed = false;
            _newVariableDescription = "";
        }

        private string GetVariableTypeName(IVariable variable)
        {
            if (variable == null) return "Unknown";

            var type = variable.GetType();
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Variable<>))
            {
                var genericArg = type.GetGenericArguments()[0];
                return genericArg.Name;
            }

            return type.Name;
        }

        private string GetVariableValueString(IVariable variable)
        {
            if (variable == null) return "null";

            var type = variable.GetType();
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Variable<>))
            {
                var valueProperty = type.GetProperty("Value");
                var value = valueProperty?.GetValue(variable);
                return value?.ToString() ?? "null";
            }

            return variable.ToString();
        }
    }
}
#endif
