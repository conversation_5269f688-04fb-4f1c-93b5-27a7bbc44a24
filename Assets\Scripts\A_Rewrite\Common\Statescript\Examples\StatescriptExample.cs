using System;
using GP.Common.Statescript;
using GP.Common.Statescript.Serialization;

namespace GP.Common.Statescript.Examples
{
    /// <summary>
    /// Example demonstrating how to create and use Statescript graphs
    /// </summary>
    public static class StatescriptExample
    {
        /// <summary>
        /// Create a simple example graph that logs messages and waits
        /// </summary>
        public static StatescriptGraph CreateSimpleExampleGraph()
        {
            var graph = new StatescriptGraph("Simple Example");
            graph.Description = "A simple example that logs messages and waits";

            // Create nodes
            var entryNode = new EntryNode();
            entryNode.Id = 1;
            entryNode.Name = "Entry";

            var logNode1 = new LogNode();
            logNode1.Id = 2;
            logNode1.Name = "Log Start";
            logNode1.SetProperty("Message", "Starting example sequence...");

            var waitNode = new WaitNode();
            waitNode.Id = 3;
            waitNode.Name = "Wait 2 Seconds";
            waitNode.SetProperty("Duration", 2.0f);

            var logNode2 = new LogNode();
            logNode2.Id = 4;
            logNode2.Name = "Log End";
            logNode2.SetProperty("Message", "Example sequence completed!");

            // Add nodes to graph
            graph.AddNode(entryNode);
            graph.AddNode(logNode1);
            graph.AddNode(waitNode);
            graph.AddNode(logNode2);

            // Create connections
            var connection1 = new StatescriptConnection(1, 2); // Entry -> Log Start
            var connection2 = new StatescriptConnection(2, 3); // Log Start -> Wait
            var connection3 = new StatescriptConnection(3, 4); // Wait -> Log End

            graph.AddConnection(connection1);
            graph.AddConnection(connection2);
            graph.AddConnection(connection3);

            return graph;
        }

        /// <summary>
        /// Create a more complex example with branching and variables
        /// </summary>
        public static StatescriptGraph CreateBranchingExampleGraph()
        {
            var graph = new StatescriptGraph("Branching Example");
            graph.Description = "Example with conditional branching and variables";

            // Add a variable
            var healthVariable = StatescriptVariable.CreateInt("Health", 100);
            graph.Variables.Add(healthVariable);

            // Create nodes
            var entryNode = new EntryNode();
            entryNode.Id = 1;

            var getHealthNode = new GetVariableNode();
            getHealthNode.Id = 2;
            getHealthNode.Name = "Get Health";
            getHealthNode.VariableName = "Health";

            var conditionNode = new ConditionNode();
            conditionNode.Id = 3;
            conditionNode.Name = "Check Health";
            conditionNode.Type = ConditionType.Variable;
            conditionNode.LeftOperand = "Health";
            conditionNode.RightOperand = "50";
            conditionNode.Operator = ComparisonOperator.Greater;

            var highHealthLog = new LogNode();
            highHealthLog.Id = 4;
            highHealthLog.Name = "High Health";
            highHealthLog.SetProperty("Message", "Health is high!");

            var lowHealthLog = new LogNode();
            lowHealthLog.Id = 5;
            lowHealthLog.Name = "Low Health";
            lowHealthLog.SetProperty("Message", "Health is low!");

            // Add nodes
            graph.AddNode(entryNode);
            graph.AddNode(getHealthNode);
            graph.AddNode(conditionNode);
            graph.AddNode(highHealthLog);
            graph.AddNode(lowHealthLog);

            // Create connections
            graph.AddConnection(new StatescriptConnection(1, 2)); // Entry -> Get Health
            graph.AddConnection(new StatescriptConnection(2, 3)); // Get Health -> Check Health

            // Conditional connections
            var trueConnection = new StatescriptConnection(3, 4); // Check Health -> High Health
            trueConnection.SetProperty("Type", "True");

            var falseConnection = new StatescriptConnection(3, 5); // Check Health -> Low Health
            falseConnection.SetProperty("Type", "False");

            graph.AddConnection(trueConnection);
            graph.AddConnection(falseConnection);

            return graph;
        }

        /// <summary>
        /// Create a looping example
        /// </summary>
        public static StatescriptGraph CreateLoopingExampleGraph()
        {
            var graph = new StatescriptGraph("Looping Example");
            graph.Description = "Example with a counting loop";

            // Add a counter variable
            var counterVariable = StatescriptVariable.CreateInt("Counter", 0);
            graph.Variables.Add(counterVariable);

            // Create nodes
            var entryNode = new EntryNode();
            entryNode.Id = 1;

            var loopNode = new LoopNode();
            loopNode.Id = 2;
            loopNode.Name = "Count to 3";
            loopNode.Type = LoopType.Count;
            loopNode.MaxIterations = 3;

            var logNode = new LogNode();
            logNode.Id = 3;
            logNode.Name = "Log Iteration";
            logNode.SetProperty("Message", "Loop iteration executed");

            var exitLogNode = new LogNode();
            exitLogNode.Id = 4;
            exitLogNode.Name = "Loop Complete";
            exitLogNode.SetProperty("Message", "Loop completed!");

            // Add nodes
            graph.AddNode(entryNode);
            graph.AddNode(loopNode);
            graph.AddNode(logNode);
            graph.AddNode(exitLogNode);

            // Create connections
            graph.AddConnection(new StatescriptConnection(1, 2)); // Entry -> Loop

            var bodyConnection = new StatescriptConnection(2, 3); // Loop -> Log (body)
            bodyConnection.SetProperty("Type", "Body");

            var exitConnection = new StatescriptConnection(2, 4); // Loop -> Exit Log
            exitConnection.SetProperty("Type", "Exit");

            graph.AddConnection(bodyConnection);
            graph.AddConnection(exitConnection);

            return graph;
        }

        /// <summary>
        /// Run an example graph with a mock context
        /// </summary>
        public static void RunExample(StatescriptGraph graph)
        {
            Console.WriteLine($"Running example graph: {graph.Name}");
            Console.WriteLine($"Description: {graph.Description}");
            Console.WriteLine();

            // Create a mock context
            var context = new StatescriptContext();

            // Initialize and start the graph
            graph.Initialize(context);
            graph.Start();

            // Simulate frame updates
            float totalTime = 0f;
            float deltaTime = 1f / 60f; // 60 FPS
            int maxFrames = 300; // 5 seconds max
            int frame = 0;

            while (graph.IsRunning && frame < maxFrames)
            {
                graph.Update(deltaTime);
                totalTime += deltaTime;
                frame++;

                // Add a small delay to see the output
                System.Threading.Thread.Sleep(16); // ~60 FPS
            }

            Console.WriteLine($"Graph execution completed in {totalTime:F2} seconds ({frame} frames)");
            Console.WriteLine();
        }

        /// <summary>
        /// Demonstrate serialization
        /// </summary>
        public static void DemonstrateSerialization()
        {
            Console.WriteLine("=== Serialization Demo ===");

            var graph = CreateSimpleExampleGraph();

            // Test different serialization formats
            var formats = new[]
            {
                StatescriptSerializationFormat.Json,
                StatescriptSerializationFormat.Binary,
                StatescriptSerializationFormat.CompressedBinary
            };

            foreach (var format in formats)
            {
                Console.WriteLine($"Testing {format} serialization...");

                try
                {
                    // Serialize
                    var data = StatescriptSerializer.Serialize(graph, format);
                    Console.WriteLine($"  Serialized size: {data.Length} bytes");

                    // Deserialize
                    var deserializedGraph = StatescriptSerializer.Deserialize(data, format);
                    Console.WriteLine($"  Deserialized successfully: {deserializedGraph.Name}");
                    Console.WriteLine($"  Node count: {deserializedGraph.Nodes.Count}");
                    Console.WriteLine($"  Connection count: {deserializedGraph.Connections.Count}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  Error: {ex.Message}");
                }

                Console.WriteLine();
            }

            // Show serialization stats
            var stats = StatescriptSerializer.GetSerializationStats(graph);
            Console.WriteLine("Serialization Statistics:");
            Console.WriteLine(stats.ToString());
            Console.WriteLine();
        }

        /// <summary>
        /// Run all examples
        /// </summary>
        public static void RunAllExamples()
        {
            Console.WriteLine("=== Statescript Examples ===");
            Console.WriteLine();

            // Run examples
            RunExample(CreateSimpleExampleGraph());
            RunExample(CreateBranchingExampleGraph());
            RunExample(CreateLoopingExampleGraph());

            // Demonstrate serialization
            DemonstrateSerialization();

            Console.WriteLine("All examples completed!");
        }
    }
}
