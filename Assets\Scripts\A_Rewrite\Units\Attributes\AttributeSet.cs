using System;
using System.Collections.Generic;

namespace Archery
{
    public class AttributeSet
    {
        private readonly Dictionary<string, IAttribute> _attributes = new Dictionary<string, IAttribute>();

        public void AddAttribute(string key, IAttribute attribute)
        {
            if (!_attributes.ContainsKey(key))
            {
                _attributes.Add(key, attribute);
            }
            else
            {
                throw new ArgumentException($"Attribute with key {key} already exists.");
            }
        }

        public Attribute<T> AddAttribute<T>(string key, T defaultValue)
        {
            if (!_attributes.TryGetValue(key, out var attribute))
            {
                // Create a new attribute of type T and add it to the collection
                attribute = new Attribute<T>(defaultValue);
                _attributes.Add(key, attribute);
                return attribute as Attribute<T>;
            }
            else if (attribute is Attribute<T> existingAttribute)
            {
                // If the attribute already exists and is of the same type, return it
                return existingAttribute;
            }
            else
            {
                throw new ArgumentException($"Attribute with key {key} already exists with a different type.");
            }
        }

        public Attribute<T> GetAttribute<T>(string key)
        {
            if (_attributes.TryGetValue(key, out var attribute))
            {
                return attribute as Attribute<T>;
            }
            throw new KeyNotFoundException($"Attribute with key {key} not found.");
        }

        public void RemoveAttribute(string key)
        {
            if (_attributes.ContainsKey(key))
            {
                _attributes.Remove(key);
            }
            else
            {
                throw new KeyNotFoundException($"Attribute with key {key} not found.");
            }
        }

        public void Clear()
        {
            _attributes.Clear();
        }
        
        public void ApplyModifiers()
        {
            foreach (var attribute in _attributes.Values)
            {
                attribute.ApplyModifiers();
            }
        }

        public void NotifyDirtyAttributes()
        {
            foreach (var attribute in _attributes.Values)
            {
                if (attribute.IsDirty)
                {
                    attribute.NotifyValueChanged();
                }
            }
        }
    }
}