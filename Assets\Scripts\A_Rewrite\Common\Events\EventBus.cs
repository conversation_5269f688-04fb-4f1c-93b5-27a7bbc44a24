using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading;
using GP.Pool;

namespace GP.Common
{
    public class DisposableBag : IDisposable
    {
        private List<IDisposable> _disposables = new List<IDisposable>();

        public void Add(IDisposable disposable)
        {
            _disposables.Add(disposable);
        }

        public void Dispose()
        {
            foreach (var disposable in _disposables)
            {
                disposable.Dispose();
            }
            _disposables.Clear();
        }
    }
    
    public sealed class Unsubscriber<T> : IDisposable where T : IEvent
    {
        private Action<T> m_Target;
        private Action<Action<T>> m_UnSub;
        public Unsubscriber<T> SetUnsubscriber(Action<Action<T>> unsub, Action<T> target)
        {
            m_Target = target;
            m_UnSub = unsub;
            return this;
        }

        public Unsubscriber<T> AddTo(DisposableBag bag)
        {
            bag.Add(this);
            return this;
        }

        public void Dispose()
        {
            m_UnSub?.Invoke(m_Target);
            m_Target = null;
            m_UnSub = null;
        }
    }
    
    /// <summary>
    /// syncronized events
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public static class EventBus<T> where T : IEvent
    {
        private static List<Action<T>> sCallbacks = new List<Action<T>>();

        public static void Publish(T evt)
        {
            for (int i = sCallbacks.Count - 1; i >= 0; i--)
            {
                sCallbacks[i](evt);
            }
        }

        public static Unsubscriber<T> Subscribe(Action<T> callback)
        {
            sCallbacks.Add(callback);

            var unsub = PoolAPI.Get<Unsubscriber<T>>();
            unsub.SetUnsubscriber(Unsubscribe, callback);
            return unsub;
        }

        public static void Unsubscribe(Action<T> callback)
        {
            sCallbacks.Remove(callback);
        }
    }

    public static class EventBusProcessor
    {
        private static List<Action> sProcessors = new List<Action>();

        internal static void Register(Action processor)
        {
            sProcessors.Add(processor);
        }

        internal static void Unregister(Action processor)
        {
            sProcessors.Remove(processor);
        }

        public static void Process()
        {
            foreach (var processor in sProcessors)
            {
                processor();
            }
        }
    }
    
    public static class AsyncEventBus<T> where T : IEvent
    {
        // Thread-safe concurrent queue for events
        private static ConcurrentQueue<T> sEventQueue = new ConcurrentQueue<T>();
        private static List<Action<T>> sCallbacks = new List<Action<T>>();
        
        static AsyncEventBus()
        {
            // Register the event processor
            EventBusProcessor.Register(Process);
        }

        // Publish is called from sub threads
        public static void Publish(T evt)
        {
            sEventQueue.Enqueue(evt);
        }

        internal static void Process()
        {
            // Process all events in the queue
            while (sEventQueue.TryDequeue(out T evt))
            {
                for (int i = sCallbacks.Count - 1; i >= 0; i--)
                {
                    sCallbacks[i](evt);
                }
            }
        }

        public static Unsubscriber<T> Subscribe(Action<T> callback)
        {
            sCallbacks.Add(callback);

            var unsub = PoolAPI.Get<Unsubscriber<T>>();
            unsub.SetUnsubscriber(Unsubscribe, callback);
            return unsub;
        }

        public static void Unsubscribe(Action<T> callback)
        {
            sCallbacks.Remove(callback);
        }
    }
}
