using System;
using System.Collections.Generic;
//using GP.Logic;

namespace GP.Common.Statescript
{
    /// <summary>
    /// Main execution engine for Statescript graphs
    /// </summary>
    public class StatescriptRunner
    {
        private readonly List<StatescriptGraph> _activeGraphs = new();
        private readonly Dictionary<int, StatescriptGraph> _graphMap = new();
        private int _nextGraphId = 1;

        /// <summary>
        /// Event fired when a graph completes execution
        /// </summary>
        public event Action<StatescriptGraph, bool> OnGraphCompleted;

        /// <summary>
        /// Event fired when a graph encounters an error
        /// </summary>
        public event Action<StatescriptGraph, string> OnGraphError;

        public StatescriptRunner()
        {
        }

        /// <summary>
        /// Start executing a graph
        /// </summary>
        public int StartGraph(StatescriptGraph graph, StatescriptContext context)
        {
            if (graph == null || context == null)
                return -1;

            try
            {
                // Initialize the graph
                graph.Initialize(context);
                
                // Validate the graph
                var errors = graph.Validate();
                if (errors.Count > 0)
                {
                    var errorMessage = $"Graph validation failed: {string.Join(", ", errors)}";
                    context.LogError(errorMessage);
                    OnGraphError?.Invoke(graph, errorMessage);
                    return -1;
                }

                // Assign an ID and start execution
                var graphId = _nextGraphId++;
                _graphMap[graphId] = graph;
                _activeGraphs.Add(graph);

                graph.Start();
                context.LogInfo($"Started Statescript graph '{graph.Name}' with ID {graphId}");
                
                return graphId;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Failed to start graph '{graph.Name}': {ex.Message}";
                context.LogError(errorMessage);
                OnGraphError?.Invoke(graph, errorMessage);
                return -1;
            }
        }

        /// <summary>
        /// Stop a running graph
        /// </summary>
        public bool StopGraph(int graphId)
        {
            if (!_graphMap.TryGetValue(graphId, out var graph))
                return false;

            try
            {
                graph.Stop();
                _activeGraphs.Remove(graph);
                _graphMap.Remove(graphId);
                
                graph.Context?.LogInfo($"Stopped Statescript graph '{graph.Name}' with ID {graphId}");
                OnGraphCompleted?.Invoke(graph, false);
                
                return true;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error stopping graph '{graph.Name}': {ex.Message}";
                graph.Context?.LogError(errorMessage);
                OnGraphError?.Invoke(graph, errorMessage);
                return false;
            }
        }

        /// <summary>
        /// Stop all running graphs
        /// </summary>
        public void StopAllGraphs()
        {
            var graphIds = new List<int>(_graphMap.Keys);
            foreach (var graphId in graphIds)
            {
                StopGraph(graphId);
            }
        }

        /// <summary>
        /// Update all active graphs
        /// </summary>
        public void Update(float deltaTime)
        {
            // Create a copy of the list to avoid modification during iteration
            var graphsToUpdate = new List<StatescriptGraph>(_activeGraphs);
            
            foreach (var graph in graphsToUpdate)
            {
                try
                {
                    if (graph.IsRunning)
                    {
                        graph.Update(deltaTime);
                        
                        // Check if graph has completed
                        if (!HasActiveNodes(graph))
                        {
                            CompleteGraph(graph, true);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error updating graph '{graph.Name}': {ex.Message}";
                    graph.Context?.LogError(errorMessage);
                    OnGraphError?.Invoke(graph, errorMessage);
                    CompleteGraph(graph, false);
                }
            }
        }

        /// <summary>
        /// Get a running graph by ID
        /// </summary>
        public StatescriptGraph GetGraph(int graphId)
        {
            _graphMap.TryGetValue(graphId, out var graph);
            return graph;
        }

        /// <summary>
        /// Get all active graphs
        /// </summary>
        public List<StatescriptGraph> GetActiveGraphs()
        {
            return new List<StatescriptGraph>(_activeGraphs);
        }

        /// <summary>
        /// Check if any graphs are currently running
        /// </summary>
        public bool HasActiveGraphs()
        {
            return _activeGraphs.Count > 0;
        }

        /// <summary>
        /// Get the number of active graphs
        /// </summary>
        public int GetActiveGraphCount()
        {
            return _activeGraphs.Count;
        }

        /// <summary>
        /// Pause a graph (stops updating but doesn't reset)
        /// </summary>
        public bool PauseGraph(int graphId)
        {
            if (!_graphMap.TryGetValue(graphId, out var graph))
                return false;

            // Remove from active list but keep in map
            _activeGraphs.Remove(graph);
            return true;
        }

        /// <summary>
        /// Resume a paused graph
        /// </summary>
        public bool ResumeGraph(int graphId)
        {
            if (!_graphMap.TryGetValue(graphId, out var graph))
                return false;

            // Add back to active list if not already there
            if (!_activeGraphs.Contains(graph))
            {
                _activeGraphs.Add(graph);
            }
            return true;
        }

        private bool HasActiveNodes(StatescriptGraph graph)
        {
            foreach (var node in graph.Nodes)
            {
                if (node.IsActive)
                    return true;
            }
            return false;
        }

        private void CompleteGraph(StatescriptGraph graph, bool success)
        {
            // Find the graph ID
            int graphId = -1;
            foreach (var kvp in _graphMap)
            {
                if (kvp.Value == graph)
                {
                    graphId = kvp.Key;
                    break;
                }
            }

            if (graphId != -1)
            {
                _activeGraphs.Remove(graph);
                _graphMap.Remove(graphId);
                
                graph.Context?.LogInfo($"Completed Statescript graph '{graph.Name}' with result: {(success ? "Success" : "Failure")}");
                OnGraphCompleted?.Invoke(graph, success);
            }
        }
    }

    /// <summary>
    /// Static helper for global Statescript operations
    /// </summary>
    public static class StatescriptManager
    {
        private static StatescriptRunner _globalRunner;

        /// <summary>
        /// Get or create the global Statescript runner
        /// </summary>
        public static StatescriptRunner GlobalRunner
        {
            get
            {
                if (_globalRunner == null)
                {
                    _globalRunner = new StatescriptRunner();
                }
                return _globalRunner;
            }
        }

        /// <summary>
        /// Start a graph using the global runner
        /// </summary>
        public static int StartGraph(StatescriptGraph graph, StatescriptContext context)
        {
            return GlobalRunner.StartGraph(graph, context);
        }

        /// <summary>
        /// Stop a graph using the global runner
        /// </summary>
        public static bool StopGraph(int graphId)
        {
            return GlobalRunner.StopGraph(graphId);
        }

        /// <summary>
        /// Update all graphs using the global runner
        /// </summary>
        public static void Update(float deltaTime)
        {
            GlobalRunner.Update(deltaTime);
        }

        /// <summary>
        /// Stop all graphs using the global runner
        /// </summary>
        public static void StopAllGraphs()
        {
            GlobalRunner.StopAllGraphs();
        }
    }
}
