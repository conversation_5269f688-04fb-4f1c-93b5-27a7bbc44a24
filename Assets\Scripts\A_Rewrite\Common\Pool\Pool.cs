using System;
using System.Collections.Generic;

namespace GP.Pool
{
    #region Pool factory
    // Default factory for types with parameterless constructor
    public class DefaultPoolFactory<T> : IPoolFactory<T> where T : class
    {
        public T Create(object key = null)
        {
            return Activator.CreateInstance(typeof(T)) as T;
        }

        public void Destroy(T obj)
        {
        }

        public void OnUse(T obj)
        {
        }

        public void OnReset(T obj)
        {
        }
    }

    // Delegate-based factory for more flexibility
    public class DelegatePoolFactory<T> : IPoolFactory<T> where T : class
    {
        private readonly Func<object, T> createFunc;
        private readonly Action<T> destroyAction;
        private readonly Action<T> onUseAction = obj => { }; // Default no-op action
        private readonly Action<T> onResetAction = obj => { }; // Default no-op action

        public DelegatePoolFactory(Func<object, T> createFunc, Action<T> destroyAction = null, Action<T> onUseAction = null, Action<T> onResetAction = null)
        {
            this.createFunc = createFunc ?? throw new ArgumentNullException(nameof(createFunc));
            this.destroyAction = destroyAction ?? (_ => { });
            this.onUseAction = onUseAction ?? (_ => { });
            this.onResetAction = onResetAction ?? (_ => { });
        }

        public T Create(object key)
        {
            return createFunc(key);
        }

        public void Destroy(T obj)
        {
            destroyAction(obj);
        }

        public void OnUse(T obj)
        {
            onUseAction(obj);
        }

        public void OnReset(T obj)
        {
            onResetAction(obj);
        }
    }
    #endregion

    #region Pool instance
    internal sealed class Pool<T> : IPool
        where T : class
    {
        static DefaultPoolFactory<T> sDefaultFactory = new DefaultPoolFactory<T>();

        private IPoolFactory<T> m_Factory;
        private Dictionary<int, Stack<T>> m_PooledObjects = new Dictionary<int, Stack<T>>();
        public int Count => m_PooledObjects.Count;

        public Pool()
        {
            if (typeof(T).GetConstructor(Type.EmptyTypes) != null)
            {
                m_Factory = sDefaultFactory;
            }
        }

        internal void SetFactory(IPoolFactory<T> factory)
        {
            m_Factory = factory;
        }

        internal T Get(object key, bool createIfNotExists)
        {
            if (TryGetFromPool(key, out T obj))
            {
                if (m_Factory != null)
                {
                    m_Factory.OnUse(obj);
                }
                return obj;
            }
            else if (createIfNotExists)
            {
                if (m_Factory != null)
                {
                    var newObj = m_Factory.Create(key);
                    return newObj;
                }
            }

            return null;
        }

        internal bool TryGetFromPool(object key, out T obj)
        {
            obj = null;
            if (m_PooledObjects.Count > 0)
            {
                var keyHash = key == null ? 0 : key.GetHashCode();
                if (m_PooledObjects.TryGetValue(keyHash, out var stack) && stack.Count > 0)
                {
                    obj = stack.Pop();
                    return true;
                }
            }
            
            return false;
        }

        internal void Free(object key, T obj)
        {
            if (obj == null) return;

            if (m_Factory != null)
            {
                m_Factory.OnReset(obj);
            }

            var keyHash = key == null ? 0 : key.GetHashCode();
            if (!m_PooledObjects.TryGetValue(keyHash, out var stack))
            {
                stack = new Stack<T>();
                m_PooledObjects.Add(keyHash, stack);
            }
            stack.Push(obj);
        }

        public void Preload(int count, object key = null)
        {
            if (m_Factory == null) return;

            for (int i = 0; i < count; i++)
            {
                var obj = m_Factory.Create(key);
                Free(key, obj);
            }
        }
        
        public void Clear()
        {
            if (m_Factory != null)
            {
                foreach (var pair in m_PooledObjects)
                {
                    var stack = pair.Value;
                    while (stack.Count > 0)
                    {
                        var obj = stack.Pop();
                        m_Factory.Destroy(obj);
                    }
                }
            }
            else
            {
                m_PooledObjects.Clear();
            }
        }
    }

    internal static class PoolMgr 
    {
        static Dictionary<Type, IPool> sPoolCache = new Dictionary<Type, IPool>();

        internal static Pool<T> GetPool<T>(bool create = true) where T : class
        {
            if (sPoolCache.TryGetValue(typeof(T), out var existingPool))
            {
                return existingPool as Pool<T>;
            }

            if (create)
            {
                var pool = new Pool<T>();
                sPoolCache[typeof(T)] = pool;
                return pool;
            }

            return null;
        }

        internal static void RegisterFactory<T>(IPoolFactory<T> factory) where T : class
        {
            var pool = GetPool<T>(true);
            pool?.SetFactory(factory);
        }

        internal static void Clear()
        {
            foreach (var pair in sPoolCache)
            {
                pair.Value.Clear();
            }

            sPoolCache.Clear();
        }
    }
    #endregion

    public static class PoolAPI 
    {
        public static void RegisterFactory<T>(IPoolFactory<T> factory) where T : class
        {
            PoolMgr.RegisterFactory<T>(factory);
        }

        public static T Get<T>(object key = null, bool createIfNotExists = true) where T : class
        {
            var pool = PoolMgr.GetPool<T>();
            return pool?.Get(key, createIfNotExists);
        }

        public static void Free<T>(T obj, object key = null) where T : class
        {
            var pool = PoolMgr.GetPool<T>();
            pool.Free(key, obj);
        }

        public static void Preload<T>(int count, object key = null) where T : class
        {
            var pool = PoolMgr.GetPool<T>();
            pool?.Preload(count, key);
        }

        public static void Clear<T>() where T : class
        {
            var pool = PoolMgr.GetPool<T>();
            pool?.Clear();
        }

        public static void ClearAll()
        {
            PoolMgr.Clear();
        }
    }
}