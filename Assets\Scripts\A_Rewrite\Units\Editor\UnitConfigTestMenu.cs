#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.IO;

namespace Archery
{
    /// <summary>
    /// Test menu items for creating sample UnitConfig and UnitStatusConfig assets
    /// </summary>
    public static class UnitConfigTestMenu
    {
        [MenuItem("Archery/Test/Create Sample Unit with Status Folder")]
        public static void CreateSampleUnitWithStatusFolder()
        {
            // Create a folder structure for testing
            var basePath = "Assets/Scripts/A_Rewrite/Units/TestData";
            var statusFolderPath = $"{basePath}/StatusConfigs";
            
            // Ensure directories exist
            if (!AssetDatabase.IsValidFolder(basePath))
            {
                Directory.CreateDirectory(basePath);
                AssetDatabase.Refresh();
            }
            
            if (!AssetDatabase.IsValidFolder(statusFolderPath))
            {
                AssetDatabase.CreateFolder(basePath, "StatusConfigs");
            }

            // Create sample status configs
            CreateSampleStatusConfig(statusFolderPath, "Hurt", 0.5f);
            CreateSampleStatusConfig(statusFolderPath, "Stunned", 2.0f);
            CreateSampleStatusConfig(statusFolderPath, "Burning", 3.0f);
            CreateSampleStatusConfig(statusFolderPath, "Frozen", 1.5f);

            // Create a sample UnitConfig
            var unitConfig = ScriptableObject.CreateInstance<UnitConfig>();
            unitConfig.id = 1001;
            unitConfig.unitName = "Test Archer";
            unitConfig.description = "A test archer unit for demonstrating status config drag-and-drop";
            unitConfig.maxHp = 100;
            unitConfig.attack = 25;
            unitConfig.attackRange = 10;
            unitConfig.attackSpeed = 2;
            unitConfig.speed = 5.0f;
            unitConfig.mass = 1.0f;
            
            // Initialize the status list (empty for testing drag-and-drop)
            unitConfig.availableStatus = new System.Collections.Generic.List<UnitStatusConfig>();

            var unitConfigPath = $"{basePath}/TestArcher.asset";
            AssetDatabase.CreateAsset(unitConfig, unitConfigPath);
            AssetDatabase.SaveAssets();

            // Select the created unit config
            Selection.activeObject = unitConfig;
            EditorGUIUtility.PingObject(unitConfig);

            Debug.Log($"Created test unit config at {unitConfigPath}");
            Debug.Log($"Created status configs folder at {statusFolderPath}");
            Debug.Log("Now you can drag the StatusConfigs folder to the 'Available Status' field in the inspector!");
        }

        [MenuItem("Archery/Test/Create Status Config Folder Only")]
        public static void CreateStatusConfigFolderOnly()
        {
            var folderPath = EditorUtility.SaveFolderPanel(
                "Choose location for Status Configs folder",
                "Assets",
                "StatusConfigs");

            if (!string.IsNullOrEmpty(folderPath))
            {
                // Convert absolute path to relative
                var relativePath = "Assets" + folderPath.Substring(Application.dataPath.Length);
                
                // Create sample status configs
                CreateSampleStatusConfig(relativePath, "Poisoned", 4.0f);
                CreateSampleStatusConfig(relativePath, "Blessed", 10.0f);
                CreateSampleStatusConfig(relativePath, "Weakened", 5.0f);

                AssetDatabase.Refresh();
                
                // Select the folder
                var folderAsset = AssetDatabase.LoadAssetAtPath<DefaultAsset>(relativePath);
                Selection.activeObject = folderAsset;
                EditorGUIUtility.PingObject(folderAsset);

                Debug.Log($"Created status configs in {relativePath}");
            }
        }

        private static void CreateSampleStatusConfig(string folderPath, string statusName, float duration)
        {
            var config = ScriptableObject.CreateInstance<UnitStatusConfig>();
            config.Name = statusName;
            config.Duration = duration;
            config.Category = eStatusCategory.Slot_1;
            config.Actions = new System.Collections.Generic.List<UnitStatusAction>();

            // Add a sample animation action
            var animAction = new PlayAnimationAction();
            animAction.Duration = duration * 0.8f; // Slightly shorter than status duration
            animAction.Delay = 0.0f;
            animAction.AnimationName = $"{statusName}Animation";
            config.Actions.Add(animAction);

            // Add a sample audio action for some statuses
            if (statusName == "Hurt" || statusName == "Burning")
            {
                var audioAction = new PlayAudioAction();
                audioAction.Duration = 0.2f;
                audioAction.Delay = 0.1f;
                config.Actions.Add(audioAction);
            }

            var assetPath = $"{folderPath}/{statusName}Status.asset";
            AssetDatabase.CreateAsset(config, assetPath);
        }

        [MenuItem("Archery/Test/Clean Test Data")]
        public static void CleanTestData()
        {
            var testDataPath = "Assets/Scripts/A_Rewrite/Units/TestData";
            
            if (AssetDatabase.IsValidFolder(testDataPath))
            {
                if (EditorUtility.DisplayDialog("Clean Test Data", 
                    $"Are you sure you want to delete the test data folder at {testDataPath}?", 
                    "Yes", "Cancel"))
                {
                    AssetDatabase.DeleteAsset(testDataPath);
                    AssetDatabase.Refresh();
                    Debug.Log("Test data cleaned up.");
                }
            }
            else
            {
                Debug.Log("No test data found to clean up.");
            }
        }
    }
}
#endif
