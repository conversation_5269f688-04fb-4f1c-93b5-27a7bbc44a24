#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;

namespace Archery
{
    /// <summary>
    /// Custom inspector for UnitConfig with folder drag-and-drop support for availableStatus
    /// </summary>
    [CustomEditor(typeof(UnitConfig))]
    public class UnitConfigEditor : Editor
    {
        private SerializedProperty _availableStatusProperty;
        private bool _isDragOver = false;
        private static readonly Color DRAG_HIGHLIGHT_COLOR = new Color(0.3f, 0.6f, 1f, 0.3f);
        private const float DROP_AREA_HEIGHT = 60f;

        private void OnEnable()
        {
            _availableStatusProperty = serializedObject.FindProperty("availableStatus");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            // Draw all properties except availableStatus using default inspector
            DrawPropertiesExcluding(serializedObject, "availableStatus");

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Available Status", EditorStyles.boldLabel);

            // Handle availableStatus with custom logic
            if (_availableStatusProperty.arraySize == 0)
            {
                DrawEmptyStatusDropArea();
            }
            else
            {
                DrawStatusList();
            }

            serializedObject.ApplyModifiedProperties();
        }

        private void DrawEmptyStatusDropArea()
        {
            var dropRect = GUILayoutUtility.GetRect(0, DROP_AREA_HEIGHT, GUILayout.ExpandWidth(true));
            
            // Handle drag and drop
            HandleDragAndDrop(dropRect);

            // Draw drop area background
            var backgroundColor = _isDragOver ? DRAG_HIGHLIGHT_COLOR : new Color(0.2f, 0.2f, 0.2f, 0.3f);
            EditorGUI.DrawRect(dropRect, backgroundColor);

            // Draw border
            var borderColor = _isDragOver ? Color.cyan : Color.gray;
            DrawRectBorder(dropRect, borderColor, 2f);

            // Draw text
            var textStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
            {
                fontSize = 14,
                normal = { textColor = _isDragOver ? Color.cyan : Color.gray }
            };

            var text = _isDragOver ? "Drop folder to add UnitStatusConfigs!" : "Drag a folder containing UnitStatusConfigs here";
            GUI.Label(dropRect, text, textStyle);

            // Add manual button as alternative
            EditorGUILayout.Space(5f);
            if (GUILayout.Button("Add Status Config Manually", GUILayout.Height(25f)))
            {
                _availableStatusProperty.arraySize++;
                serializedObject.ApplyModifiedProperties();
            }
        }

        private void DrawStatusList()
        {
            // Show the list using Unity's default property field
            EditorGUILayout.PropertyField(_availableStatusProperty, true);

            EditorGUILayout.Space(5f);

            // Add utility buttons
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Add Status Config", GUILayout.Height(25f)))
            {
                _availableStatusProperty.arraySize++;
                serializedObject.ApplyModifiedProperties();
            }

            if (GUILayout.Button("Clear All", GUILayout.Height(25f)))
            {
                if (EditorUtility.DisplayDialog("Clear All Status Configs",
                    "Are you sure you want to remove all status configs?", "Yes", "Cancel"))
                {
                    _availableStatusProperty.ClearArray();
                    serializedObject.ApplyModifiedProperties();
                }
            }

            if (GUILayout.Button("Add Folder", GUILayout.Height(25f)))
            {
                ShowFolderSelectionDialog();
            }

            EditorGUILayout.EndHorizontal();

            // Show drag area hint even when list has items
            EditorGUILayout.Space(5f);
            var hintRect = GUILayoutUtility.GetRect(0, 30f, GUILayout.ExpandWidth(true));
            HandleDragAndDrop(hintRect);

            if (_isDragOver)
            {
                EditorGUI.DrawRect(hintRect, DRAG_HIGHLIGHT_COLOR);
                var hintStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel)
                {
                    normal = { textColor = Color.cyan }
                };
                GUI.Label(hintRect, "Drop folder to add more UnitStatusConfigs!", hintStyle);
            }
            else
            {
                var hintStyle = new GUIStyle(EditorStyles.centeredGreyMiniLabel);
                GUI.Label(hintRect, "You can also drag folders here to add more configs", hintStyle);
            }
        }

        private void HandleDragAndDrop(Rect dropArea)
        {
            var currentEvent = Event.current;
            var eventType = currentEvent.type;

            if (eventType == EventType.DragUpdated || eventType == EventType.DragPerform)
            {
                if (dropArea.Contains(currentEvent.mousePosition))
                {
                    var draggedObjects = DragAndDrop.objectReferences;
                    var hasValidFolder = false;

                    // Check if any dragged objects are folders containing UnitStatusConfigs
                    foreach (var obj in draggedObjects)
                    {
                        if (obj is DefaultAsset) // Folder
                        {
                            var path = AssetDatabase.GetAssetPath(obj);
                            if (AssetDatabase.IsValidFolder(path))
                            {
                                var configs = FindUnitStatusConfigsInFolder(path);
                                if (configs.Count > 0)
                                {
                                    hasValidFolder = true;
                                    break;
                                }
                            }
                        }
                        else if (obj is UnitStatusConfig)
                        {
                            hasValidFolder = true;
                            break;
                        }
                    }

                    if (hasValidFolder)
                    {
                        DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                        _isDragOver = eventType == EventType.DragUpdated;

                        if (eventType == EventType.DragPerform)
                        {
                            DragAndDrop.AcceptDrag();
                            AddDraggedConfigs(draggedObjects);
                            _isDragOver = false;
                        }

                        currentEvent.Use();
                    }
                    else
                    {
                        _isDragOver = false;
                    }
                }
                else
                {
                    _isDragOver = false;
                }
            }
            else if (eventType == EventType.DragExited)
            {
                _isDragOver = false;
            }
        }

        private void AddDraggedConfigs(Object[] draggedObjects)
        {
            var configsToAdd = new List<UnitStatusConfig>();

            foreach (var obj in draggedObjects)
            {
                if (obj is DefaultAsset) // Folder
                {
                    var path = AssetDatabase.GetAssetPath(obj);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        var configs = FindUnitStatusConfigsInFolder(path);
                        configsToAdd.AddRange(configs);
                    }
                }
                else if (obj is UnitStatusConfig config)
                {
                    configsToAdd.Add(config);
                }
            }

            // Remove duplicates
            var existingConfigs = new HashSet<UnitStatusConfig>();
            for (int i = 0; i < _availableStatusProperty.arraySize; i++)
            {
                var element = _availableStatusProperty.GetArrayElementAtIndex(i);
                if (element.objectReferenceValue is UnitStatusConfig existing)
                {
                    existingConfigs.Add(existing);
                }
            }

            var addedCount = 0;
            foreach (var config in configsToAdd)
            {
                if (!existingConfigs.Contains(config))
                {
                    _availableStatusProperty.arraySize++;
                    var newElement = _availableStatusProperty.GetArrayElementAtIndex(_availableStatusProperty.arraySize - 1);
                    newElement.objectReferenceValue = config;
                    addedCount++;
                }
            }

            serializedObject.ApplyModifiedProperties();

            if (addedCount > 0)
            {
                Debug.Log($"Added {addedCount} UnitStatusConfig(s) to {target.name}");
            }
            else
            {
                Debug.Log("No new UnitStatusConfigs were added (duplicates filtered out)");
            }
        }

        private List<UnitStatusConfig> FindUnitStatusConfigsInFolder(string folderPath)
        {
            var configs = new List<UnitStatusConfig>();
            var guids = AssetDatabase.FindAssets("t:UnitStatusConfig", new[] { folderPath });

            foreach (var guid in guids)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                var config = AssetDatabase.LoadAssetAtPath<UnitStatusConfig>(assetPath);
                if (config != null)
                {
                    configs.Add(config);
                }
            }

            return configs;
        }

        private void ShowFolderSelectionDialog()
        {
            var folderPath = EditorUtility.OpenFolderPanel(
                "Select folder containing UnitStatusConfigs",
                "Assets",
                "");

            if (!string.IsNullOrEmpty(folderPath))
            {
                // Convert absolute path to relative
                var relativePath = "Assets" + folderPath.Substring(Application.dataPath.Length);
                var configs = FindUnitStatusConfigsInFolder(relativePath);

                if (configs.Count > 0)
                {
                    AddDraggedConfigs(new Object[] { AssetDatabase.LoadAssetAtPath<DefaultAsset>(relativePath) });
                }
                else
                {
                    EditorUtility.DisplayDialog("No Configs Found",
                        $"No UnitStatusConfig assets found in the selected folder:\n{relativePath}",
                        "OK");
                }
            }
        }

        private void DrawRectBorder(Rect rect, Color color, float thickness)
        {
            // Top
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, rect.width, thickness), color);
            // Bottom
            EditorGUI.DrawRect(new Rect(rect.x, rect.yMax - thickness, rect.width, thickness), color);
            // Left
            EditorGUI.DrawRect(new Rect(rect.x, rect.y, thickness, rect.height), color);
            // Right
            EditorGUI.DrawRect(new Rect(rect.xMax - thickness, rect.y, thickness, rect.height), color);
        }
    }
}
#endif
