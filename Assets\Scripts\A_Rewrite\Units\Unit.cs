using System.Collections.Generic;
using UnityEngine;
using GP.Pool;
using GP.Common;

namespace Archery
{
    public class Unit : MonoBehaviour
    {
        public int ObjId { get { return GetInstanceID(); } } // 角色实例ID
        public UnitConfig UnitConfig { get; protected set; } // 角色配置
        public AttributeSet Attributes { get; protected set; } = new AttributeSet(); // 角色属性

        public List<IUnitComponent> UnitComponents { get; protected set; } = new List<IUnitComponent>(); // 角色组件列表
        public List<UnitStatus> UnitStatusList { get; protected set; } = new List<UnitStatus>(); // 状态列表

        protected Dictionary<int, UnitStatus> activeStatus = new Dictionary<int, UnitStatus>(); // 状态映射

        public void OnCreate(UnitConfig config)
        {
            UnitConfig = config;
            UnitManager.Register(this); // 注册角色

            CreateAttributes(); // 创建属性
            InitializeStatus(); // 初始化状态
            InitializeComponents(); // 初始化组件
            foreach (var component in UnitComponents)
            {
                component.OnCreate(this); // 初始化组件
            }
        }

        protected virtual void CreateAttributes()
        {
            Attributes.AddAttribute("MaxHp", UnitConfig.maxHp); // 添加最大生命值属性
            Attributes.AddAttribute("Hp", UnitConfig.maxHp); // 添加当前生命值属性
            Attributes.AddAttribute("Attack", UnitConfig.attack); // 添加攻击力属性
            Attributes.AddAttribute("AttackRange", UnitConfig.attackRange); // 添加攻击范围属性
            Attributes.AddAttribute("AttackSpeed", UnitConfig.attackSpeed); // 添加攻击速度属性
            Attributes.AddAttribute("Speed", UnitConfig.speed); // 添加速度属性
            Attributes.AddAttribute("Mass", UnitConfig.mass); // 添加质量属性
        }

        protected virtual void InitializeStatus()
        {
            foreach (var statusConfig in UnitConfig.availableStatus)
            {
                var status = new UnitStatus(statusConfig);
                UnitStatusList.Add(status);
            }
        }

        protected virtual void InitializeComponents()
        {
            GetComponents<IUnitComponent>(UnitComponents); // 获取所有组件
        }

        public T GetUnitComponent<T>() where T : IUnitComponent
        {
            foreach (var component in UnitComponents)
            {
                if (component is T t)
                {
                    return t; // 如果找到组件，返回
                }
            }
            return default; // 如果没有找到，返回默认值
        }

        public bool HasUnitComponent<T>() where T : IUnitComponent
        {
            foreach (var component in UnitComponents)
            {
                if (component is T)
                {
                    return true; // 如果找到组件，返回true
                }
            }
            return false; // 如果没有找到，返回false
        }

        public void AddUnitComponent<T>() where T : IUnitComponent, new()
        {
            var component = new T();
            UnitComponents.Add(component); // 添加组件
            component.OnCreate(this); // 初始化组件
        }

        public void AddUnitComponentBase<T>() where T : UnitComponentBase
        {
            var component = GetComponentInChildren<T>();
            if (component == null)
            {
                component = gameObject.AddComponent<T>();
            }
            UnitComponents.Add(component); // 添加组件
            component.OnCreate(this); // 初始化组件
        }

        public void RemoveUnitComponent<T>() where T : IUnitComponent
        {
            foreach (var component in UnitComponents)
            {
                if (component is T t)
                {
                    UnitComponents.Remove(t); // 移除组件
                    t.OnReset(); // 重置组件
                    break;
                }
            }
        }

        private void Update()
        {
            // 更新组件
            foreach (var component in UnitComponents)
            {
                component.OnUpdate();
            }

            UpdateState(Time.deltaTime);
        }

        private void OnDestroy()
        {

        }

        public void OnUse()
        {
            gameObject.SetActive(true); 
        }

        public void OnReset()
        {
            gameObject.SetActive(false); 
            
            Attributes.Clear(); // 清除属性
            UnitManager.Unregister(this); // 取消注册角色
        }

        // TODO: Replace OnHurt & OnDie with UnitStates, eg: Unit.SetState()
        public void RunState(string stateName)
        {
            var state = GetState(stateName);
            if (state != null)
            {
                int stateSlot = state.Category;
                var runningState = GetRunningState(stateSlot);
                if (runningState != null)
                {
                    runningState.Abort(this); // 重置正在运行的状态
                }

                // Set running status
                activeStatus[stateSlot] = state;

                // Start the state
                state.Start(this);
            }
        }

        public UnitStatus GetRunningState(int stateSlot)
        {
            if (activeStatus.TryGetValue(stateSlot, out var status))
            {
                return status; // 返回正在运行的状态
            }
            return null; // 如果没有找到，返回null
        }

        UnitStatus GetState(string stateName)
        {
            foreach (var status in UnitStatusList)
            {
                if (status.Config.Name == stateName)
                {
                    return status;
                }
            }
            return null;
        }

        void UpdateState(float deltaTime)
        {
            // 清理已完成的状态
            var completedStates = new List<int>(); //PoolAPI.Get<List<int>>();
            // 更新状态
            foreach (var pair in activeStatus)
            {
                var status = pair.Value;
                if (status.IsRunning)
                {
                    status.Update(this, deltaTime); // 更新状态
                }
                if (!status.IsRunning)
                {
                    completedStates.Add(pair.Key);
                }
            }

            // 移除已完成的状态
            foreach (var stateSlot in completedStates)
            {
                activeStatus.Remove(stateSlot); // 从活动状态中移除
            }
        }

        public void AddEffect<T>()
        {

        }

        public void RemoveEffect<T>()
        {
            
        }

        public virtual void OnHurt(eDamageType damageType, float damage)
        {
            var hp = Attributes.GetAttribute<float>("Hp");
            if (hp != null)
            {
                hp.CurrentValue -= damage; // 减少生命值
                if (hp.CurrentValue <= 0)
                {
                    hp.CurrentValue = 0; // 确保生命值不小于0
                    OnDie(); // 角色死亡处理
                }
            }
        }

        public virtual void OnDie()
        {
            // 角色死亡处理
        }
    }
}