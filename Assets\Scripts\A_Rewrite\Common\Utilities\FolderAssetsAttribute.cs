using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Common
{
    [AttributeUsage(AttributeTargets.Field)]
    public class FolderAssetsAttribute : PropertyAttribute
    {
        public readonly Type AssetType;

        public FolderAssetsAttribute(Type assetType)
        {
            this.AssetType = assetType;
        }
    }

#if UNITY_EDITOR
    [CustomPropertyDrawer(typeof(FolderAssetsAttribute))]
    public class FolderAssetsDrawer : PropertyDrawer
    {
        private static Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
        private const float HEADER_HEIGHT = 22f;
        private const float ITEM_HEIGHT = 20f;
        private const float BUTTON_HEIGHT = 18f;
        private const float SPACING = 2f;
        private const float INDENT = 15f;
        private bool _isDragOver = false;
        private static readonly Color DRAG_HIGHLIGHT_COLOR = new Color(0.3f, 0.6f, 1f, 0.3f);

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            // Check if this property is part of a list (individual element)
            if (IsListElement(property))
            {
                // For individual list elements, just show the drop area
                return EditorGUIUtility.singleLineHeight;
            }

            // For the actual list property, show full interface
            if (!property.isArray)
                return EditorGUIUtility.singleLineHeight;

            var foldoutKey = property.propertyPath;
            var isExpanded = _foldoutStates.ContainsKey(foldoutKey) ? _foldoutStates[foldoutKey] : true;

            var height = HEADER_HEIGHT + SPACING;
            if (isExpanded)
            {
                height += BUTTON_HEIGHT + SPACING;
                height += property.arraySize * (ITEM_HEIGHT + SPACING);
                height += SPACING;
            }

            return height;
        }

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var folderAssetsAttribute = (FolderAssetsAttribute)attribute;

            // Check if this is a list element (not the list itself)
            if (IsListElement(property))
            {
                DrawListElementDropArea(position, property, label, folderAssetsAttribute);
                return;
            }

            // Handle the actual list property
            if (!property.isArray)
            {
                EditorGUI.LabelField(position, label.text, "FolderAssets can only be used on arrays/lists");
                return;
            }

            DrawListInterface(position, property, label, folderAssetsAttribute);
        }

        private bool IsListElement(SerializedProperty property)
        {
            // Check if this property is an element of an array (contains ".Array.data[")
            return property.propertyPath.Contains(".Array.data[");
        }

        private void DrawListElementDropArea(Rect position, SerializedProperty property, GUIContent label, FolderAssetsAttribute folderAssetsAttribute)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Find the parent list property
            var listProperty = GetParentListProperty(property);
            if (listProperty == null)
            {
                EditorGUI.PropertyField(position, property, label);
                EditorGUI.EndProperty();
                return;
            }

            // Handle drag and drop
            HandleDragAndDrop(position, listProperty, folderAssetsAttribute);

            // Draw drop area with highlight
            if (_isDragOver)
            {
                EditorGUI.DrawRect(position, DRAG_HIGHLIGHT_COLOR);
            }

            var infoText = _isDragOver ? "Drop folder here!" : "Drop folder here";
            var style = new GUIStyle(EditorStyles.centeredGreyMiniLabel);
            if (_isDragOver) style.normal.textColor = Color.cyan;

            EditorGUI.LabelField(position, infoText, style);

            EditorGUI.EndProperty();
        }

        private SerializedProperty GetParentListProperty(SerializedProperty elementProperty)
        {
            var path = elementProperty.propertyPath;
            var arrayDataIndex = path.LastIndexOf(".Array.data[");
            if (arrayDataIndex == -1) return null;

            var listPath = path.Substring(0, arrayDataIndex);
            return elementProperty.serializedObject.FindProperty(listPath);
        }

        private void DrawListInterface(Rect position, SerializedProperty property, GUIContent label, FolderAssetsAttribute folderAssetsAttribute)
        {
            EditorGUI.BeginProperty(position, label, property);

            var currentY = position.y;
            var foldoutKey = property.propertyPath;

            // Header with foldout
            var headerRect = new Rect(position.x, currentY, position.width, HEADER_HEIGHT);
            var isExpanded = _foldoutStates.ContainsKey(foldoutKey) ? _foldoutStates[foldoutKey] : true;

            // Handle drag and drop over the entire area
            HandleDragAndDrop(position, property, folderAssetsAttribute);

            // Draw header with drag highlight
            if (_isDragOver)
            {
                EditorGUI.DrawRect(headerRect, DRAG_HIGHLIGHT_COLOR);
            }

            var newExpanded = EditorGUI.Foldout(headerRect, isExpanded,
                $"{label.text} ({property.arraySize})", true);

            if (newExpanded != isExpanded)
            {
                _foldoutStates[foldoutKey] = newExpanded;
            }

            currentY += HEADER_HEIGHT + SPACING;

            if (newExpanded)
            {
                EditorGUI.indentLevel++;

                // Draw buttons
                var buttonRect = new Rect(position.x + INDENT, currentY, position.width - INDENT, BUTTON_HEIGHT);
                DrawButtons(buttonRect, property, folderAssetsAttribute);
                currentY += BUTTON_HEIGHT + SPACING;

                // Draw list items
                for (int i = 0; i < property.arraySize; i++)
                {
                    var itemRect = new Rect(position.x + INDENT, currentY, position.width - INDENT, ITEM_HEIGHT);
                    DrawListItem(itemRect, property, i, folderAssetsAttribute);
                    currentY += ITEM_HEIGHT + SPACING;
                }

                EditorGUI.indentLevel--;
            }

            EditorGUI.EndProperty();
        }

        private void HandleDragAndDrop(Rect dropArea, SerializedProperty property, FolderAssetsAttribute folderAssetsAttribute)
        {
            var currentEvent = Event.current;
            var eventType = currentEvent.type;

            if (eventType == EventType.DragUpdated || eventType == EventType.DragPerform)
            {
                if (dropArea.Contains(currentEvent.mousePosition))
                {
                    var draggedObjects = DragAndDrop.objectReferences;
                    var hasValidObjects = false;

                    // Check if any dragged objects are folders or assets of the correct type
                    foreach (var obj in draggedObjects)
                    {
                        if (obj is DefaultAsset) // Folder
                        {
                            var path = AssetDatabase.GetAssetPath(obj);
                            if (AssetDatabase.IsValidFolder(path))
                            {
                                var assets = FindAssetsInFolder(path, folderAssetsAttribute.AssetType);
                                if (assets.Count > 0)
                                {
                                    hasValidObjects = true;
                                    break;
                                }
                            }
                        }
                        else if (folderAssetsAttribute.AssetType.IsAssignableFrom(obj.GetType()))
                        {
                            hasValidObjects = true;
                            break;
                        }
                    }

                    if (hasValidObjects)
                    {
                        DragAndDrop.visualMode = DragAndDropVisualMode.Copy;
                        _isDragOver = eventType == EventType.DragUpdated;

                        if (eventType == EventType.DragPerform)
                        {
                            DragAndDrop.AcceptDrag();
                            AddDraggedObjects(property, draggedObjects, folderAssetsAttribute);
                            _isDragOver = false;
                        }

                        currentEvent.Use();
                    }
                    else
                    {
                        _isDragOver = false;
                    }
                }
                else
                {
                    _isDragOver = false;
                }
            }
            else if (eventType == EventType.DragExited)
            {
                _isDragOver = false;
            }
        }

        private void AddDraggedObjects(SerializedProperty property, UnityEngine.Object[] draggedObjects, FolderAssetsAttribute folderAssetsAttribute)
        {
            var assetsToAdd = new List<UnityEngine.Object>();

            foreach (var obj in draggedObjects)
            {
                if (obj is DefaultAsset) // Folder
                {
                    var path = AssetDatabase.GetAssetPath(obj);
                    if (AssetDatabase.IsValidFolder(path))
                    {
                        var assets = FindAssetsInFolder(path, folderAssetsAttribute.AssetType);
                        assetsToAdd.AddRange(assets);
                    }
                }
                else if (folderAssetsAttribute.AssetType.IsAssignableFrom(obj.GetType()))
                {
                    assetsToAdd.Add(obj);
                }
            }

            // Remove duplicates and add to property
            var existingAssets = new HashSet<UnityEngine.Object>();
            for (int i = 0; i < property.arraySize; i++)
            {
                var element = property.GetArrayElementAtIndex(i);
                if (element.objectReferenceValue != null)
                {
                    existingAssets.Add(element.objectReferenceValue);
                }
            }

            foreach (var asset in assetsToAdd)
            {
                if (!existingAssets.Contains(asset))
                {
                    property.arraySize++;
                    var newElement = property.GetArrayElementAtIndex(property.arraySize - 1);
                    newElement.objectReferenceValue = asset;
                }
            }

            property.serializedObject.ApplyModifiedProperties();
            Debug.Log($"Added {assetsToAdd.Count} assets of type {folderAssetsAttribute.AssetType.Name}");
        }

        private List<UnityEngine.Object> FindAssetsInFolder(string folderPath, Type assetType)
        {
            var assets = new List<UnityEngine.Object>();
            var guids = AssetDatabase.FindAssets($"t:{assetType.Name}", new[] { folderPath });

            foreach (var guid in guids)
            {
                var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                var asset = AssetDatabase.LoadAssetAtPath(assetPath, assetType);
                if (asset != null)
                {
                    assets.Add(asset);
                }
            }

            return assets;
        }

        private void DrawButtons(Rect rect, SerializedProperty property, FolderAssetsAttribute folderAssetsAttribute)
        {
            var buttonWidth = 60f;
            var spacing = 5f;

            // Add button
            var addRect = new Rect(rect.x, rect.y, buttonWidth, rect.height);
            if (GUI.Button(addRect, "Add", EditorStyles.miniButtonLeft))
            {
                property.arraySize++;
                property.serializedObject.ApplyModifiedProperties();
            }

            // Clear button
            var clearRect = new Rect(addRect.xMax + spacing, rect.y, buttonWidth, rect.height);
            if (GUI.Button(clearRect, "Clear", EditorStyles.miniButtonRight))
            {
                if (EditorUtility.DisplayDialog("Clear All",
                    $"Are you sure you want to remove all {folderAssetsAttribute.AssetType.Name} assets?", "Yes", "Cancel"))
                {
                    property.ClearArray();
                    property.serializedObject.ApplyModifiedProperties();
                }
            }

            // Info text
            var infoRect = new Rect(clearRect.xMax + spacing * 2, rect.y, rect.width - clearRect.xMax - spacing * 2, rect.height);
            var infoStyle = new GUIStyle(EditorStyles.miniLabel)
            {
                alignment = TextAnchor.MiddleLeft,
                normal = { textColor = _isDragOver ? Color.cyan : Color.gray }
            };
            var infoText = _isDragOver ? "Drop to add assets!" : $"Drag folders or {folderAssetsAttribute.AssetType.Name} assets here";
            GUI.Label(infoRect, infoText, infoStyle);
        }

        private void DrawListItem(Rect rect, SerializedProperty property, int index, FolderAssetsAttribute folderAssetsAttribute)
        {
            var element = property.GetArrayElementAtIndex(index);
            var buttonWidth = 20f;

            // Object field
            var objectRect = new Rect(rect.x, rect.y, rect.width - buttonWidth - 5f, rect.height);
            EditorGUI.PropertyField(objectRect, element, GUIContent.none);

            // Remove button
            var removeRect = new Rect(objectRect.xMax + 5f, rect.y, buttonWidth, rect.height);
            if (GUI.Button(removeRect, "×", EditorStyles.miniButton))
            {
                property.DeleteArrayElementAtIndex(index);
                property.serializedObject.ApplyModifiedProperties();
            }
        }
    }
    #endif

}