using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Common
{
    [AttributeUsage(AttributeTargets.Field)]
    public class FolderAssetsAttribute : PropertyAttribute
    {
        public readonly Type AssetType;

        public FolderAssetsAttribute(Type assetType)
        {
            this.AssetType = assetType;
        }
    }

#if UNITY_EDITOR
    [CustomPropertyDrawer(typeof(FolderAssetsAttribute))]
    public class FolderAssetsDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var folderAssetsAttribute = (FolderAssetsAttribute)attribute;
            
            EditorGUI.BeginProperty(position, label, property);
            
            // Draw the property label
            position = EditorGUI.PrefixLabel(position, GUIUtility.GetControlID(FocusType.Passive), label);
            
            // Create drop area
            var dropRect = new Rect(position.x, position.y, position.width, EditorGUIUtility.singleLineHeight);
            
            // Handle drag and drop
            Event evt = Event.current;
            
            if (dropRect.Contains(evt.mousePosition))
            {
                if (evt.type == EventType.DragUpdated)
                {
                    // Check if any dragged objects are folders
                    bool hasFolder = DragAndDrop.objectReferences.Any(obj => 
                        AssetDatabase.IsValidFolder(AssetDatabase.GetAssetPath(obj)));
                    
                    DragAndDrop.visualMode = hasFolder ? DragAndDropVisualMode.Copy : DragAndDropVisualMode.Rejected;
                    evt.Use();
                }
                else if (evt.type == EventType.DragPerform)
                {
                    DragAndDrop.AcceptDrag();
                    
                    foreach (var obj in DragAndDrop.objectReferences)
                    {
                        string path = AssetDatabase.GetAssetPath(obj);
                        if (AssetDatabase.IsValidFolder(path))
                        {
                            PopulateArrayFromFolder(property, path, folderAssetsAttribute.AssetType);
                        }
                    }
                    
                    evt.Use();
                }
            }
            
            // Draw current array size info
            string infoText = property.isArray ? $"Array Size: {property.arraySize}" : "Drop folder here";
            EditorGUI.LabelField(dropRect, infoText, EditorStyles.centeredGreyMiniLabel);
            
            EditorGUI.EndProperty();
        }
        
        private void PopulateArrayFromFolder(SerializedProperty property, string folderPath, Type assetType)
        {
            if (!property.isArray)
            {
                Debug.LogWarning("FolderAssetsAttribute can only be used on arrays or lists.");
                return;
            }
            
            // Find all assets of the specified type in the folder
            string[] guids = AssetDatabase.FindAssets($"t:{assetType.Name}", new[] { folderPath });
            List<UnityEngine.Object> assets = new List<UnityEngine.Object>();
            
            foreach (string guid in guids)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                var asset = AssetDatabase.LoadAssetAtPath(assetPath, assetType);
                if (asset != null)
                {
                    assets.Add(asset);
                }
            }
            
            // Clear and populate the array
            property.ClearArray();
            property.arraySize = assets.Count;
            
            for (int i = 0; i < assets.Count; i++)
            {
                property.GetArrayElementAtIndex(i).objectReferenceValue = assets[i];
            }
            
            property.serializedObject.ApplyModifiedProperties();
            
            Debug.Log($"Populated array with {assets.Count} assets of type {assetType.Name} from folder: {folderPath}");
        }
    }
    #endif

}