using System;
using System.Collections.Generic;
using UnityEngine;

namespace Archery
{
    /// <summary>
    /// State runner
    /// </summary>
    public class UnitStatus
    {
        public UnitStatusConfig Config { get; private set; }
        public bool IsRunning { get; private set; } = false;
        public float ElapsedTime { get; private set; } = 0f;
        public float Duration => Config?.Duration ?? 0f;
        public List<UnitStatusAction> Actions => Config?.Actions ?? new List<UnitStatusAction>();
        public int Category => (int)Config.Category;

        public UnitStatus(UnitStatusConfig config)
        {
            Config = config ?? throw new ArgumentNullException(nameof(config));
        }

        public void Reset()
        {
            IsRunning = false;
            ElapsedTime = 0f;
        }

        public void Start(Unit target)
        {
            Reset();
            IsRunning = true;

            // Execute all actions on the target unit
            foreach (var action in Actions)
            {
                action.Execute(target);
            }
        }

        public void Update(Unit target, float deltaTime)
        {
            if (!IsRunning) return;

            ElapsedTime += deltaTime;

            // Check if the status duration has expired
            if (ElapsedTime >= Duration)
            {
                Reset();
            }
            else
            {
                // Execute all actions on the target unit
                foreach (var action in Actions)
                {
                    action.Tick(target, deltaTime);
                }
            }
        }

        public void Abort(Unit target)
        {
            if (!IsRunning) return;

            // Abort all actions on the target unit
            foreach (var action in Actions)
            {
                action.Abort(target);
            }
            
            Reset();
        }
    }
}