#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace Archery
{
    /// <summary>
    /// Test menu items for creating sample UnitStatusConfig assets
    /// </summary>
    public static class UnitStatusConfigTestMenu
    {
        [MenuItem("Archery/Test/Create Sample Status Config")]
        public static void CreateSampleStatusConfig()
        {
            // Create a new UnitStatusConfig
            var config = ScriptableObject.CreateInstance<UnitStatusConfig>();
            config.Name = "OnHurt";
            config.Duration = 0.5f;
            config.Category = eStatusCategory.Slot_1;

            // Initialize the Actions list
            config.Actions = new System.Collections.Generic.List<UnitStatusAction>();

            // Add sample actions
            var animAction = new PlayAnimationAction();
            animAction.Duration = 0.3f;
            animAction.Delay = 0.0f;
            animAction.AnimationName = "HurtAnimation";
            config.Actions.Add(animAction);

            var audioAction = new PlayAudioAction();
            audioAction.Duration = 0.2f;
            audioAction.Delay = 0.1f;
            config.Actions.Add(audioAction);

            var effectAction = new PlayEffectAction();
            effectAction.Duration = 0.5f;
            effectAction.Delay = 0.0f;
            config.Actions.Add(effectAction);

            // Create the asset
            var path = "Assets/Scripts/A_Rewrite/Units/Status/SampleStatusConfig.asset";
            AssetDatabase.CreateAsset(config, path);
            AssetDatabase.SaveAssets();

            // Select the created asset
            Selection.activeObject = config;
            EditorGUIUtility.PingObject(config);

            Debug.Log($"Created sample UnitStatusConfig at {path}");
        }

        [MenuItem("Archery/Test/Create Empty Status Config")]
        public static void CreateEmptyStatusConfig()
        {
            // Create a new empty UnitStatusConfig
            var config = ScriptableObject.CreateInstance<UnitStatusConfig>();
            config.Name = "NewStatus";
            config.Duration = 1.0f;
            config.Category = eStatusCategory.Slot_1;
            config.Actions = new System.Collections.Generic.List<UnitStatusAction>();

            // Create the asset
            var path = EditorUtility.SaveFilePanelInProject(
                "Save UnitStatusConfig",
                "NewStatusConfig",
                "asset",
                "Choose where to save the new UnitStatusConfig");

            if (!string.IsNullOrEmpty(path))
            {
                AssetDatabase.CreateAsset(config, path);
                AssetDatabase.SaveAssets();

                // Select the created asset
                Selection.activeObject = config;
                EditorGUIUtility.PingObject(config);

                Debug.Log($"Created empty UnitStatusConfig at {path}");
            }
        }
    }
}
#endif
